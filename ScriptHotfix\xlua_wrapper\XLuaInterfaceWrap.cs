﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif
using XLua;
using System;
using System.Collections.Generic;
namespace XLua.CSObjectWrap{
    public class SystemCollectionsIEnumeratorBridge : LuaBase, System.Collections.IEnumerator {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new SystemCollectionsIEnumeratorBridge(reference, luaenv);
		}
		public SystemCollectionsIEnumeratorBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		bool System.Collections.IEnumerator.MoveNext(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("MoveNext")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "MoveNext");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("MoveNext");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.IEnumerator.Reset(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Reset")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Reset");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Reset");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        object System.Collections.IEnumerator.Current
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "Current");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					object __gen_ret = translator.GetObject(L, -1, typeof(object));
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class SystemCollectionsGenericIList_1_SystemObject_Bridge : LuaBase, System.Collections.Generic.IList<object> {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new SystemCollectionsGenericIList_1_SystemObject_Bridge(reference, luaenv);
		}
		public SystemCollectionsGenericIList_1_SystemObject_Bridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		int System.Collections.Generic.IList<object>.IndexOf(object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("IndexOf")){
						return default(int);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "IndexOf");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("IndexOf");
						return default(int);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				int __gen_ret = LuaAPI.xlua_tointeger(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<object>.Insert(int index, object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Insert")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Insert");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Insert");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				translator.PushAny(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<object>.RemoveAt(int index){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("RemoveAt")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "RemoveAt");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("RemoveAt");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.Generic.IEnumerator<object> System.Collections.Generic.IEnumerable<object>.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.Generic.IEnumerator<object>);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.Generic.IEnumerator<object>);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.Generic.IEnumerator<object> __gen_ret = (System.Collections.Generic.IEnumerator<object>)translator.GetObject(L, err_func + 1, typeof(System.Collections.Generic.IEnumerator<object>));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.IEnumerator);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.IEnumerator);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.IEnumerator __gen_ret = (System.Collections.IEnumerator)translator.GetObject(L, err_func + 1, typeof(System.Collections.IEnumerator));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<object>.Add(object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Add")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Add");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Add");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<object>.Clear(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Clear")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Clear");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Clear");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<object>.Contains(object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Contains")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Contains");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Contains");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<object>.CopyTo(object[] array, int arrayIndex){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("CopyTo")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "CopyTo");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("CopyTo");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, array);
				LuaAPI.xlua_pushinteger(L, arrayIndex);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<object>.Remove(object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Remove")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Remove");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Remove");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        int System.Collections.Generic.ICollection<object>.Count
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "Count");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        bool System.Collections.Generic.ICollection<object>.IsReadOnly
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "IsReadOnly");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					bool __gen_ret = LuaAPI.lua_toboolean(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        object System.Collections.Generic.IList<object>.this[int index]
		{
		    get	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "get_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function get_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					object __gen_ret = translator.GetObject(L, err_func + 1, typeof(object));
					LuaAPI.lua_settop(L, err_func - 1);
					return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
			set	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "set_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function set_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					translator.PushAny(L, value);
					int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class SystemCollectionsGenericIList_1_SystemDouble_Bridge : LuaBase, System.Collections.Generic.IList<double> {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new SystemCollectionsGenericIList_1_SystemDouble_Bridge(reference, luaenv);
		}
		public SystemCollectionsGenericIList_1_SystemDouble_Bridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		int System.Collections.Generic.IList<double>.IndexOf(double item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("IndexOf")){
						return default(int);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "IndexOf");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("IndexOf");
						return default(int);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushnumber(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				int __gen_ret = LuaAPI.xlua_tointeger(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<double>.Insert(int index, double item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Insert")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Insert");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Insert");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				LuaAPI.lua_pushnumber(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<double>.RemoveAt(int index){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("RemoveAt")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "RemoveAt");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("RemoveAt");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.Generic.IEnumerator<double> System.Collections.Generic.IEnumerable<double>.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.Generic.IEnumerator<double>);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.Generic.IEnumerator<double>);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.Generic.IEnumerator<double> __gen_ret = (System.Collections.Generic.IEnumerator<double>)translator.GetObject(L, err_func + 1, typeof(System.Collections.Generic.IEnumerator<double>));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.IEnumerator);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.IEnumerator);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.IEnumerator __gen_ret = (System.Collections.IEnumerator)translator.GetObject(L, err_func + 1, typeof(System.Collections.IEnumerator));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<double>.Add(double item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Add")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Add");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Add");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushnumber(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<double>.Clear(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Clear")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Clear");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Clear");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<double>.Contains(double item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Contains")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Contains");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Contains");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushnumber(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<double>.CopyTo(double[] array, int arrayIndex){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("CopyTo")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "CopyTo");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("CopyTo");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, array);
				LuaAPI.xlua_pushinteger(L, arrayIndex);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<double>.Remove(double item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Remove")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Remove");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Remove");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushnumber(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        int System.Collections.Generic.ICollection<double>.Count
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "Count");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        bool System.Collections.Generic.ICollection<double>.IsReadOnly
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "IsReadOnly");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					bool __gen_ret = LuaAPI.lua_toboolean(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        double System.Collections.Generic.IList<double>.this[int index]
		{
		    get	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "get_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function get_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					double __gen_ret = LuaAPI.lua_tonumber(L, err_func + 1);
					LuaAPI.lua_settop(L, err_func - 1);
					return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
			set	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "set_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function set_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					LuaAPI.lua_pushnumber(L, value);
					int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class SystemCollectionsGenericIList_1_UnityEngineObject_Bridge : LuaBase, System.Collections.Generic.IList<UnityEngine.Object> {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new SystemCollectionsGenericIList_1_UnityEngineObject_Bridge(reference, luaenv);
		}
		public SystemCollectionsGenericIList_1_UnityEngineObject_Bridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		int System.Collections.Generic.IList<UnityEngine.Object>.IndexOf(UnityEngine.Object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("IndexOf")){
						return default(int);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "IndexOf");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("IndexOf");
						return default(int);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				int __gen_ret = LuaAPI.xlua_tointeger(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<UnityEngine.Object>.Insert(int index, UnityEngine.Object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Insert")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Insert");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Insert");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				translator.Push(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.IList<UnityEngine.Object>.RemoveAt(int index){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("RemoveAt")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "RemoveAt");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("RemoveAt");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, index);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.Generic.IEnumerator<UnityEngine.Object> System.Collections.Generic.IEnumerable<UnityEngine.Object>.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.Generic.IEnumerator<UnityEngine.Object>);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.Generic.IEnumerator<UnityEngine.Object>);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.Generic.IEnumerator<UnityEngine.Object> __gen_ret = (System.Collections.Generic.IEnumerator<UnityEngine.Object>)translator.GetObject(L, err_func + 1, typeof(System.Collections.Generic.IEnumerator<UnityEngine.Object>));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("GetEnumerator")){
						return default(System.Collections.IEnumerator);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "GetEnumerator");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("GetEnumerator");
						return default(System.Collections.IEnumerator);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				System.Collections.IEnumerator __gen_ret = (System.Collections.IEnumerator)translator.GetObject(L, err_func + 1, typeof(System.Collections.IEnumerator));
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<UnityEngine.Object>.Add(UnityEngine.Object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Add")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Add");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Add");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<UnityEngine.Object>.Clear(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Clear")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Clear");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Clear");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<UnityEngine.Object>.Contains(UnityEngine.Object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Contains")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Contains");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Contains");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void System.Collections.Generic.ICollection<UnityEngine.Object>.CopyTo(UnityEngine.Object[] array, int arrayIndex){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("CopyTo")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "CopyTo");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("CopyTo");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, array);
				LuaAPI.xlua_pushinteger(L, arrayIndex);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool System.Collections.Generic.ICollection<UnityEngine.Object>.Remove(UnityEngine.Object item){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Remove")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Remove");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Remove");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, item);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        int System.Collections.Generic.ICollection<UnityEngine.Object>.Count
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "Count");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        bool System.Collections.Generic.ICollection<UnityEngine.Object>.IsReadOnly
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "IsReadOnly");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					bool __gen_ret = LuaAPI.lua_toboolean(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        UnityEngine.Object System.Collections.Generic.IList<UnityEngine.Object>.this[int index]
		{
		    get	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "get_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function get_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					UnityEngine.Object __gen_ret = (UnityEngine.Object)translator.GetObject(L, err_func + 1, typeof(UnityEngine.Object));
					LuaAPI.lua_settop(L, err_func - 1);
					return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
			set	{
#if THREAD_SAFE || HOTFIX_ENABLE
				lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "set_Item");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					if(!LuaAPI.lua_isfunction(L, -1)){
						LuaAPI.xlua_pushasciistring(L, "no such function set_Item");
						luaEnv.ThrowExceptionFromError(err_func - 1);
					}
					LuaAPI.lua_pushvalue(L, -2);
					LuaAPI.lua_remove(L, -3);
					LuaAPI.xlua_pushinteger(L, index);
					translator.Push(L, value);
					int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
					if (__gen_error != 0)
						luaEnv.ThrowExceptionFromError(err_func - 1);
					LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
				}
#endif
			}
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class dlaisceneIPlotAnnouncePanelBridge : LuaBase, dl.ai.scene.IPlotAnnouncePanel {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new dlaisceneIPlotAnnouncePanelBridge(reference, luaenv);
		}
		public dlaisceneIPlotAnnouncePanelBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		string dl.ai.scene.IPlotAnnouncePanel.getPanel(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("getPanel")){
						return default(string);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "getPanel");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("getPanel");
						return default(string);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				string __gen_ret = LuaAPI.lua_tostring(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void dl.ai.scene.IPlotAnnouncePanel.show(string str){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("show")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "show");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("show");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushstring(L, str);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void dl.ai.scene.IPlotAnnouncePanel.hide(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("hide")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "hide");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("hide");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameuiISpaceSkillListenerBridge : LuaBase, game.ui.ISpaceSkillListener {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameuiISpaceSkillListenerBridge(reference, luaenv);
		}
		public gameuiISpaceSkillListenerBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void game.ui.ISpaceSkillListener.onComplete(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onComplete")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onComplete");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onComplete");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gamemonomanagerISceneDataBridge : LuaBase, game.mono.manager.ISceneData {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gamemonomanagerISceneDataBridge(reference, luaenv);
		}
		public gamemonomanagerISceneDataBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
        long game.mono.manager.ISceneData.id
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "id");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
            set
            {
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "id");
					LuaAPI.lua_pushint64(L, value);
					if (0 != LuaAPI.xlua_psettable(L, -3)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					LuaAPI.lua_pop(L, 1);
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.mono.manager.ISceneData.placeId
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "placeId");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.mono.manager.ISceneData.sceneType
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "sceneType");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.mono.manager.ISceneData.file
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "file");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.mono.manager.ISceneData.extraFile
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "extraFile");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.mono.manager.ISceneData.machine
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "machine");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameaientityIDropDataBridge : LuaBase, game.ai.entity.IDropData {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameaientityIDropDataBridge(reference, luaenv);
		}
		public gameaientityIDropDataBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		bool game.ai.entity.IDropData.canPick(long entity){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("canPick")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "canPick");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("canPick");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushint64(L, entity);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		bool game.ai.entity.IDropData.isPicked(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("isPicked")){
						return default(bool);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "isPicked");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("isPicked");
						return default(bool);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				bool __gen_ret = LuaAPI.lua_toboolean(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        long game.ai.entity.IDropData.id
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "id");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        long game.ai.entity.IDropData.dropper
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "dropper");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.ai.entity.IDropData.color
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "color");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.ai.entity.IDropData.text
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "text");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.ai.entity.IDropData.prefab
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "prefab");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        UnityEngine.Vector3 game.ai.entity.IDropData.src
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "src");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					UnityEngine.Vector3 __gen_ret;translator.Get(L, -1, out __gen_ret);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        UnityEngine.Vector3 game.ai.entity.IDropData.dst
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "dst");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					UnityEngine.Vector3 __gen_ret;translator.Get(L, -1, out __gen_ret);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        double game.ai.entity.IDropData.begtime
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "begtime");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					double __gen_ret = LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.ai.entity.IDropData.duetime
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "duetime");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.ai.entity.IDropData.dropType
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "dropType");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameaiscenetreeIPlotPanelBridge : LuaBase, game.ai.scenetree.IPlotPanel {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameaiscenetreeIPlotPanelBridge(reference, luaenv);
		}
		public gameaiscenetreeIPlotPanelBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void game.ai.scenetree.IPlotPanel.show(int plotId, bool canQuick, bool canProcess){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("show")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "show");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("show");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, plotId);
				LuaAPI.lua_pushboolean(L, canQuick);
				LuaAPI.lua_pushboolean(L, canProcess);
				int __gen_error = LuaAPI.lua_pcall(L, 4, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.ai.scenetree.IPlotPanel.hide(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("hide")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "hide");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("hide");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameaiscenetreeIPlotSpeakPanelBridge : LuaBase, game.ai.scenetree.IPlotSpeakPanel {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameaiscenetreeIPlotSpeakPanelBridge(reference, luaenv);
		}
		public gameaiscenetreeIPlotSpeakPanelBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void game.ai.scenetree.IPlotSpeakPanel.talk(XLua.LuaTable tb){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("talk")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "talk");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("talk");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, tb);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		string game.ai.scenetree.IPlotSpeakPanel.getPanel(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("getPanel")){
						return default(string);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "getPanel");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("getPanel");
						return default(string);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				string __gen_ret = LuaAPI.lua_tostring(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.ai.scenetree.IPlotPanel.show(int plotId, bool canQuick, bool canProcess){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("show")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "show");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("show");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, plotId);
				LuaAPI.lua_pushboolean(L, canQuick);
				LuaAPI.lua_pushboolean(L, canProcess);
				int __gen_error = LuaAPI.lua_pcall(L, 4, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.ai.scenetree.IPlotPanel.hide(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("hide")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "hide");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("hide");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameaimachinevoIAttackSourceBridge : LuaBase, game.ai.machine.vo.IAttackSource {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameaimachinevoIAttackSourceBridge(reference, luaenv);
		}
		public gameaimachinevoIAttackSourceBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		float game.ai.machine.vo.IAttackSource.getBuffProperty(int type){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("getBuffProperty")){
						return default(float);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "getBuffProperty");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("getBuffProperty");
						return default(float);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, type);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				float __gen_ret = (float)LuaAPI.lua_tonumber(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		int game.ai.machine.vo.IAttackSource.canBreak(game.ai.machine.vo.IAttackSource other){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("canBreak")){
						return default(int);	
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "canBreak");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("canBreak");
						return default(int);	
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, other);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 1, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				int __gen_ret = LuaAPI.xlua_tointeger(L, err_func + 1);
				LuaAPI.lua_settop(L, err_func - 1);
				return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.ai.machine.vo.IAttackSource.setStyle(int type, string style){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("setStyle")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "setStyle");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("setStyle");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.xlua_pushinteger(L, type);
				LuaAPI.lua_pushstring(L, style);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        object game.ai.machine.vo.IAttackSource.fxlist
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "fxlist");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					object __gen_ret = translator.GetObject(L, -1, typeof(object));
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.ai.machine.vo.IAttackSource.minRange
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "minRange");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.ai.machine.vo.IAttackSource.maxRange
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "maxRange");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.ai.machine.vo.IAttackSource.animSpeed
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "animSpeed");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.ai.machine.vo.IAttackSource.fireRangeMul
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "fireRangeMul");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.ai.machine.vo.IAttackSource.locateMode
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "locateMode");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.ai.machine.vo.IAttackSource.locatePrefab
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "locatePrefab");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        long game.ai.machine.vo.IAttackSource.skillId
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "skillId");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.ai.machine.vo.IAttackSource.typeSkillId
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "typeSkillId");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.ai.machine.vo.IAttackSource.fireMode
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "fireMode");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gametutorITutorNodeBridge : LuaBase, game.tutor.ITutorNode {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gametutorITutorNodeBridge(reference, luaenv);
		}
		public gametutorITutorNodeBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
        long game.tutor.ITutorNode.groupId
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "groupId");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        long game.tutor.ITutorNode.guid
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "guid");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					long __gen_ret = LuaAPI.lua_toint64(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.tutor.ITutorNode.tutorKey
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "tutorKey");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.tutor.ITutorNode.prefab
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "prefab");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.tutor.ITutorNode.status
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "status");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
            set
            {
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "status");
					LuaAPI.xlua_pushinteger(L, value);
					if (0 != LuaAPI.xlua_psettable(L, -3)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					LuaAPI.lua_pop(L, 1);
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.tutor.ITutorNode.maxCount
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "maxCount");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        string game.tutor.ITutorNode.description
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "description");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.tutor.ITutorNode.duration
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "duration");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        UnityEngine.Vector2 game.tutor.ITutorNode.extend
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "extend");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					UnityEngine.Vector2 __gen_ret;translator.Get(L, -1, out __gen_ret);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        bool game.tutor.ITutorNode.autoSize
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "autoSize");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					bool __gen_ret = LuaAPI.lua_toboolean(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        int game.tutor.ITutorNode.shape
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "shape");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					int __gen_ret = LuaAPI.xlua_tointeger(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        object game.tutor.ITutorNode.targetObject
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					WrapPusher translator = luaEnv.translator as WrapPusher;
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "targetObject");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					object __gen_ret = translator.GetObject(L, -1, typeof(object));
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gamemediatorbarILuaBarBridge : LuaBase, game.mediator.bar.ILuaBar {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gamemediatorbarILuaBarBridge(reference, luaenv);
		}
		public gamemediatorbarILuaBarBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void game.mediator.bar.ILuaBar.setPhase(game.ui.UIRendererBase bar, int phase){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("setPhase")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "setPhase");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("setPhase");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				LuaAPI.xlua_pushinteger(L, phase);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.onInit(game.ui.UIRendererBase bar){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onInit")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onInit");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onInit");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.onUpdate(game.ui.UIRendererBase bar, float loaded, float total, float speed){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onUpdate")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onUpdate");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onUpdate");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				LuaAPI.lua_pushnumber(L, loaded);
				LuaAPI.lua_pushnumber(L, total);
				LuaAPI.lua_pushnumber(L, speed);
				int __gen_error = LuaAPI.lua_pcall(L, 5, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.onErrorCode(game.ui.UIRendererBase bar, int errCode){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onErrorCode")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onErrorCode");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onErrorCode");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				LuaAPI.xlua_pushinteger(L, errCode);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.onError(game.ui.UIRendererBase bar, string errCode){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onError")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onError");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onError");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				LuaAPI.lua_pushstring(L, errCode);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.onDestroy(game.ui.UIRendererBase bar){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onDestroy")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onDestroy");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onDestroy");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.Push(L, bar);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void game.mediator.bar.ILuaBar.Dispose(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Dispose")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Dispose");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Dispose");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class gameluaIEpisodeBridge : LuaBase, game.lua.IEpisode {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new gameluaIEpisodeBridge(reference, luaenv);
		}
		public gameluaIEpisodeBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void game.lua.IEpisode.onQuickComplete(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onQuickComplete")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onQuickComplete");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onQuickComplete");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        string game.lua.IEpisode.prefab
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "prefab");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					string __gen_ret = LuaAPI.lua_tostring(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        float game.lua.IEpisode.duration
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "duration");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					float __gen_ret = (float)LuaAPI.lua_tonumber(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
        bool game.lua.IEpisode.precast
        {
            get{
#if THREAD_SAFE || HOTFIX_ENABLE
                lock (luaEnv.luaEnvLock){
#endif
					RealStatePtr L = luaEnv.L;
					int oldTop = LuaAPI.lua_gettop(L);
					LuaAPI.lua_getref(L, luaReference);
					LuaAPI.xlua_pushasciistring(L, "precast");
					if (0 != LuaAPI.xlua_pgettable(L, -2)){
						luaEnv.ThrowExceptionFromError(oldTop);
					}
					bool __gen_ret = LuaAPI.lua_toboolean(L, -1);
					LuaAPI.lua_pop(L, 2);
					return __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
                }
#endif
            }
        }
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class purestateMachineinterfacesILuaActionBridge : LuaBase, pure.stateMachine.interfaces.ILuaAction {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new purestateMachineinterfacesILuaActionBridge(reference, luaenv);
		}
		public purestateMachineinterfacesILuaActionBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void pure.stateMachine.interfaces.ILuaAction.onEnter(object ctx, object data){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onEnter")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onEnter");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onEnter");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, ctx);
				translator.PushAny(L, data);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void pure.stateMachine.interfaces.ILuaAction.onUpdate(object ctx, double now){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onUpdate")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onUpdate");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onUpdate");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, ctx);
				LuaAPI.lua_pushnumber(L, now);
				int __gen_error = LuaAPI.lua_pcall(L, 3, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void pure.stateMachine.interfaces.ILuaAction.onExit(object ctx){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onExit")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				WrapPusher translator = luaEnv.translator as WrapPusher;
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onExit");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onExit");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				translator.PushAny(L, ctx);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void pure.stateMachine.interfaces.ILuaAction.Dispose(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Dispose")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Dispose");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Dispose");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class pureluaILuaActionCallBackBridge : LuaBase, pure.lua.ILuaActionCallBack {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new pureluaILuaActionCallBackBridge(reference, luaenv);
		}
		public pureluaILuaActionCallBackBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void pure.lua.ILuaActionCallBack.onComplete(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("onComplete")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "onComplete");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("onComplete");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void pure.lua.ILuaActionCallBack.Dispose(){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("Dispose")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "Dispose");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("Dispose");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				int __gen_error = LuaAPI.lua_pcall(L, 1, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
    public class purenetsocketLuaServerIParserBridge : LuaBase, pure.net.socket.LuaServer.IParser {
    	private List<string> _errorFunc;
	    public static LuaBase __Create(int reference, LuaEnv luaenv){
		    return new purenetsocketLuaServerIParserBridge(reference, luaenv);
		}
		public purenetsocketLuaServerIParserBridge(int reference, LuaEnv luaenv) : base(reference, luaenv){
        }
		void pure.net.socket.LuaServer.IParser.parse(byte[] data){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("parse")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "parse");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("parse");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushstring(L, data);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		void pure.net.socket.LuaServer.IParser.setConnected(bool val){
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (luaEnv.luaEnvLock){
#endif
				if(_errorFunc != null && _errorFunc.Contains("setConnected")){
					 return;
				}
				RealStatePtr L = luaEnv.L;
				int err_func = LuaAPI.load_error_func(L, luaEnv.errorFuncRef);
				LuaAPI.lua_getref(L, luaReference);
				LuaAPI.xlua_pushasciistring(L, "setConnected");
				if (0 != LuaAPI.xlua_pgettable(L, -2)){
					luaEnv.ThrowExceptionFromError(err_func - 1);
				}
				if(!LuaAPI.lua_isfunction(L, -1)){
					LuaAPI.lua_settop(L, err_func - 1);
					if (_errorFunc == null) _errorFunc =  pure.utils.memory.ListPool<string>.Get();
					_errorFunc.Add("setConnected");
					 return;
				}
				LuaAPI.lua_pushvalue(L, -2);
				LuaAPI.lua_remove(L, -3);
				LuaAPI.lua_pushboolean(L, val);
				int __gen_error = LuaAPI.lua_pcall(L, 2, 0, err_func);
				if (__gen_error != 0)
					luaEnv.ThrowExceptionFromError(err_func - 1);
				LuaAPI.lua_settop(L, err_func - 1);
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
		protected override void DisposeSelf() {
			if(_errorFunc!=null) pure.utils.memory.ListPool<string>.Release(_errorFunc);
			_errorFunc = null;
		}
	}
}
