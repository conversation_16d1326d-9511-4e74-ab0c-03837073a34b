﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using System;


namespace XLua{
    public static class DelegateBridgeWrap{
		
		private static void __Gen_Delegate_Imp0(this DelegateBridge db  )
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                
                db.PCall(L, 0, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp1(this DelegateBridge db  
			,bool p0)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushboolean(L, p0);
                
                db.PCall(L, 1, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static double __Gen_Delegate_Imp2(this DelegateBridge db  
			,double p0
			,double p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushnumber(L, p0);
                LuaAPI.lua_pushnumber(L, p1);
                
                db.PCall(L, 2, 1, errFunc);

                
                double __gen_ret = LuaAPI.lua_tonumber(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static XLua.LuaTable __Gen_Delegate_Imp3(this DelegateBridge db  )
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                
                db.PCall(L, 0, 1, errFunc);

                
                XLua.LuaTable __gen_ret = (XLua.LuaTable)translator.GetObject(L, errFunc + 1, typeof(XLua.LuaTable));
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp4(this DelegateBridge db  
			,string p0)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushstring(L, p0);
                
                db.PCall(L, 1, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp5(this DelegateBridge db  
			,double p0)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushnumber(L, p0);
                
                db.PCall(L, 1, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp6(this DelegateBridge db  
			,string p0
			,UnityEngine.Vector3 p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.lua_pushstring(L, p0);
                translator.PushUnityEngineVector3(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp7(this DelegateBridge db  
			,string p0
			,game.ui.ListEvent p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.lua_pushstring(L, p0);
                translator.Push(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp8(this DelegateBridge db  
			,string p0
			,game.mono.DragData p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.lua_pushstring(L, p0);
                translator.Push(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp9(this DelegateBridge db  
			,string p0
			,game.mono.DragData p1
			,game.mono.DropStatus p2)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.lua_pushstring(L, p0);
                translator.Push(L, p1);
                translator.Push(L, p2);
                
                db.PCall(L, 3, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp10(this DelegateBridge db  
			,string p0
			,bool p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushstring(L, p0);
                LuaAPI.lua_pushboolean(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp11(this DelegateBridge db  
			,string p0
			,int p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushstring(L, p0);
                LuaAPI.xlua_pushinteger(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp12(this DelegateBridge db  
			,string p0
			,string p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushstring(L, p0);
                LuaAPI.lua_pushstring(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp13(this DelegateBridge db  
			,string p0
			,float p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                
                LuaAPI.lua_pushstring(L, p0);
                LuaAPI.lua_pushnumber(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp14(this DelegateBridge db  
			,string p0
			,game.ui.ScrollVO p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.lua_pushstring(L, p0);
                translator.PushgameuiScrollVO(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static bool __Gen_Delegate_Imp15(this DelegateBridge db  
			,pure.entity.interfaces.IAuxiliary p0)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                
                db.PCall(L, 1, 1, errFunc);

                
                bool __gen_ret = LuaAPI.lua_toboolean(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static bool __Gen_Delegate_Imp16(this DelegateBridge db  
			,pure.entity.interfaces.IAuxiliary p0
			,double p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                LuaAPI.lua_pushnumber(L, p1);
                
                db.PCall(L, 2, 1, errFunc);

                
                bool __gen_ret = LuaAPI.lua_toboolean(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static bool __Gen_Delegate_Imp17(this DelegateBridge db  
			,pure.entity.interfaces.IAuxiliary p0
			,UnityEngine.GameObject p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                translator.Push(L, p1);
                
                db.PCall(L, 2, 1, errFunc);

                
                bool __gen_ret = LuaAPI.lua_toboolean(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static bool __Gen_Delegate_Imp18(this DelegateBridge db  
			,pure.entity.interfaces.IAuxiliary p0
			,UnityEngine.Transform p1
			,double p2)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                translator.Push(L, p1);
                LuaAPI.lua_pushnumber(L, p2);
                
                db.PCall(L, 3, 1, errFunc);

                
                bool __gen_ret = LuaAPI.lua_toboolean(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp19(this DelegateBridge db  
			,int p0
			,game.mediator.MediatorCore p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                LuaAPI.xlua_pushinteger(L, p0);
                translator.Push(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp20(this DelegateBridge db  
			,game.ai.entity.ISceneObject p0
			,int p1)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                LuaAPI.xlua_pushinteger(L, p1);
                
                db.PCall(L, 2, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static XLua.LuaTable __Gen_Delegate_Imp21(this DelegateBridge db  
			,game.ui.IDataPane p0
			,object p1
			,int p2)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                translator.PushAny(L, p1);
                LuaAPI.xlua_pushinteger(L, p2);
                
                db.PCall(L, 3, 1, errFunc);

                
                XLua.LuaTable __gen_ret = (XLua.LuaTable)translator.GetObject(L, errFunc + 1, typeof(XLua.LuaTable));
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static int __Gen_Delegate_Imp22(this DelegateBridge db  
			,game.ui.IDataPane p0
			,object p1
			,int p2)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                translator.PushAny(L, p1);
                LuaAPI.xlua_pushinteger(L, p2);
                
                db.PCall(L, 3, 1, errFunc);

                
                int __gen_ret = LuaAPI.xlua_tointeger(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static string __Gen_Delegate_Imp23(this DelegateBridge db  
			,game.ui.IDataPane p0
			,object p1
			,int p2)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.PushAny(L, p0);
                translator.PushAny(L, p1);
                LuaAPI.xlua_pushinteger(L, p2);
                
                db.PCall(L, 3, 1, errFunc);

                
                string __gen_ret = LuaAPI.lua_tostring(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp24(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,int p3
			,int p4
			,int p5)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                LuaAPI.xlua_pushinteger(L, p3);
                LuaAPI.xlua_pushinteger(L, p4);
                LuaAPI.xlua_pushinteger(L, p5);
                
                db.PCall(L, 6, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp25(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,bool p3)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                LuaAPI.lua_pushboolean(L, p3);
                
                db.PCall(L, 4, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp26(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,int p3
			,int p4)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                LuaAPI.xlua_pushinteger(L, p3);
                LuaAPI.xlua_pushinteger(L, p4);
                
                db.PCall(L, 5, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp27(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,int p3
			,game.ui.ISelectableItem p4
			,object p5
			,int p6)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                LuaAPI.xlua_pushinteger(L, p3);
                translator.PushAny(L, p4);
                translator.PushAny(L, p5);
                LuaAPI.xlua_pushinteger(L, p6);
                
                db.PCall(L, 7, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static bool __Gen_Delegate_Imp28(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,XLua.LuaTable p3
			,object p4)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                translator.Push(L, p3);
                translator.PushAny(L, p4);
                
                db.PCall(L, 5, 1, errFunc);

                
                bool __gen_ret = LuaAPI.lua_toboolean(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static string __Gen_Delegate_Imp29(this DelegateBridge db  
			,XLua.LuaTable p0
			,game.ui.ISelectableItem p1
			,object p2
			,int p3
			,int p4)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                translator.PushAny(L, p1);
                translator.PushAny(L, p2);
                LuaAPI.xlua_pushinteger(L, p3);
                LuaAPI.xlua_pushinteger(L, p4);
                
                db.PCall(L, 5, 1, errFunc);

                
                string __gen_ret = LuaAPI.lua_tostring(L, errFunc + 1);
                LuaAPI.lua_settop(L, errFunc - 1);
                return  __gen_ret;
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        
		private static void __Gen_Delegate_Imp30(this DelegateBridge db  
			,pure.utils.tween.TweenCore p0)
		{
#if THREAD_SAFE || HOTFIX_ENABLE
            lock (db.luaEnv.luaEnvLock){
#endif
                RealStatePtr L = db.luaEnv.rawL;
                int errFunc = LuaAPI.pcall_prepare(L, db.errorFuncRef, db.luaReference);
                WrapPusher translator = db.luaEnv.translator as WrapPusher;
                translator.Push(L, p0);
                
                db.PCall(L, 1, 0, errFunc);

                
                
                LuaAPI.lua_settop(L, errFunc - 1);
                
#if THREAD_SAFE || HOTFIX_ENABLE
            }
#endif
		}
        

		static DelegateBridgeWrap(){
		    DelegateBridge.Gen_Flag = true;
		    DelegateBridge.RegRetriever = GetDelegateByType;
		}
		public static void a(){}

		private static  Delegate GetDelegateByType(DelegateBridge db, Type type){
		
		    if (type == typeof(System.Action)){
			    return new System.Action(db.__Gen_Delegate_Imp0);
			}
		
		    if (type == typeof(UnityEngine.Events.UnityAction)){
			    return new UnityEngine.Events.UnityAction(db.__Gen_Delegate_Imp0);
			}
		
		    if (type == typeof(pure.utils.memory.ReportAsset.SnapComplete)){
			    return new pure.utils.memory.ReportAsset.SnapComplete(db.__Gen_Delegate_Imp0);
			}
		
		    if (type == typeof(System.Action<bool>)){
			    return new System.Action<bool>(db.__Gen_Delegate_Imp1);
			}
		
		    if (type == typeof(System.Func<double, double, double>)){
			    return new System.Func<double, double, double>(db.__Gen_Delegate_Imp2);
			}
		
		    if (type == typeof(System.Func<XLua.LuaTable>)){
			    return new System.Func<XLua.LuaTable>(db.__Gen_Delegate_Imp3);
			}
		
		    if (type == typeof(System.Action<string>)){
			    return new System.Action<string>(db.__Gen_Delegate_Imp4);
			}
		
		    if (type == typeof(System.Action<double>)){
			    return new System.Action<double>(db.__Gen_Delegate_Imp5);
			}
		
		    if (type == typeof(System.Action<string, UnityEngine.Vector3>)){
			    return new System.Action<string, UnityEngine.Vector3>(db.__Gen_Delegate_Imp6);
			}
		
		    if (type == typeof(System.Action<string, game.ui.ListEvent>)){
			    return new System.Action<string, game.ui.ListEvent>(db.__Gen_Delegate_Imp7);
			}
		
		    if (type == typeof(System.Action<string, game.mono.DragData>)){
			    return new System.Action<string, game.mono.DragData>(db.__Gen_Delegate_Imp8);
			}
		
		    if (type == typeof(System.Action<string, game.mono.DragData, game.mono.DropStatus>)){
			    return new System.Action<string, game.mono.DragData, game.mono.DropStatus>(db.__Gen_Delegate_Imp9);
			}
		
		    if (type == typeof(System.Action<string, bool>)){
			    return new System.Action<string, bool>(db.__Gen_Delegate_Imp10);
			}
		
		    if (type == typeof(System.Action<string, int>)){
			    return new System.Action<string, int>(db.__Gen_Delegate_Imp11);
			}
		
		    if (type == typeof(System.Action<string, string>)){
			    return new System.Action<string, string>(db.__Gen_Delegate_Imp12);
			}
		
		    if (type == typeof(System.Action<string, float>)){
			    return new System.Action<string, float>(db.__Gen_Delegate_Imp13);
			}
		
		    if (type == typeof(pure.utils.memory.ReportAsset.SnapUpdate)){
			    return new pure.utils.memory.ReportAsset.SnapUpdate(db.__Gen_Delegate_Imp13);
			}
		
		    if (type == typeof(System.Action<string, game.ui.ScrollVO>)){
			    return new System.Action<string, game.ui.ScrollVO>(db.__Gen_Delegate_Imp14);
			}
		
		    if (type == typeof(game.ai.entity.OnInternalAction)){
			    return new game.ai.entity.OnInternalAction(db.__Gen_Delegate_Imp15);
			}
		
		    if (type == typeof(game.ai.entity.OnInternalUpdate)){
			    return new game.ai.entity.OnInternalUpdate(db.__Gen_Delegate_Imp16);
			}
		
		    if (type == typeof(game.ai.entity.OnInternalModelAction)){
			    return new game.ai.entity.OnInternalModelAction(db.__Gen_Delegate_Imp17);
			}
		
		    if (type == typeof(game.ai.entity.OnInternalModelUpdate)){
			    return new game.ai.entity.OnInternalModelUpdate(db.__Gen_Delegate_Imp18);
			}
		
		    if (type == typeof(game.mediator.MediatorEvent)){
			    return new game.mediator.MediatorEvent(db.__Gen_Delegate_Imp19);
			}
		
		    if (type == typeof(game.ui.MapManager.OnEntityChanged)){
			    return new game.ui.MapManager.OnEntityChanged(db.__Gen_Delegate_Imp20);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.CreateItem)){
			    return new game.ui.GridFactory_Lua.CreateItem(db.__Gen_Delegate_Imp21);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.PickPrefab)){
			    return new game.ui.GridFactory_Lua.PickPrefab(db.__Gen_Delegate_Imp22);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.PickSkin)){
			    return new game.ui.GridFactory_Lua.PickSkin(db.__Gen_Delegate_Imp23);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.On_Normal)){
			    return new game.ui.GridFactory_Lua.On_Normal(db.__Gen_Delegate_Imp24);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.On_SelectChange)){
			    return new game.ui.GridFactory_Lua.On_SelectChange(db.__Gen_Delegate_Imp25);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.On_Drag)){
			    return new game.ui.GridFactory_Lua.On_Drag(db.__Gen_Delegate_Imp26);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.On_Drop)){
			    return new game.ui.GridFactory_Lua.On_Drop(db.__Gen_Delegate_Imp27);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.Is_Dropable)){
			    return new game.ui.GridFactory_Lua.Is_Dropable(db.__Gen_Delegate_Imp28);
			}
		
		    if (type == typeof(game.ui.GridFactory_Lua.GetFadeControl)){
			    return new game.ui.GridFactory_Lua.GetFadeControl(db.__Gen_Delegate_Imp29);
			}
		
		    if (type == typeof(pure.utils.tween.TweenCallback)){
			    return new pure.utils.tween.TweenCallback(db.__Gen_Delegate_Imp30);
			}
		
		    return null;
		}
	}

}