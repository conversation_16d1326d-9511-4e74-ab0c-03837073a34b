﻿

<linker>

	<assembly fullname="mscorlib">
	    <type fullname="System.Object" preserve="all"/>
		<type fullname="System.Collections.Generic.List`1[System.Int32]" preserve="all"/>
		<type fullname="System.Collections.Generic.IList`1[System.Object]" preserve="all"/>
		<type fullname="System.Collections.Generic.IList`1[System.Double]" preserve="all"/>
		<type fullname="System.ValueType" preserve="all"/>
		<type fullname="System.Enum" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.CoreModule">
	    <type fullname="UnityEngine.Object" preserve="all"/>
		<type fullname="UnityEngine.Vector2" preserve="all"/>
		<type fullname="UnityEngine.Vector3" preserve="all"/>
		<type fullname="UnityEngine.Vector4" preserve="all"/>
		<type fullname="UnityEngine.Quaternion" preserve="all"/>
		<type fullname="UnityEngine.Color" preserve="all"/>
		<type fullname="UnityEngine.Ray" preserve="all"/>
		<type fullname="UnityEngine.Bounds" preserve="all"/>
		<type fullname="UnityEngine.Ray2D" preserve="all"/>
		<type fullname="UnityEngine.Time" preserve="all"/>
		<type fullname="UnityEngine.GameObject" preserve="all"/>
		<type fullname="UnityEngine.Component" preserve="all"/>
		<type fullname="UnityEngine.Behaviour" preserve="all"/>
		<type fullname="UnityEngine.Transform" preserve="all"/>
		<type fullname="UnityEngine.Resources" preserve="all"/>
		<type fullname="UnityEngine.TextAsset" preserve="all"/>
		<type fullname="UnityEngine.Keyframe" preserve="all"/>
		<type fullname="UnityEngine.AnimationCurve" preserve="all"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="all"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all"/>
		<type fullname="UnityEngine.Renderer" preserve="all"/>
		<type fullname="UnityEngine.Light" preserve="all"/>
		<type fullname="UnityEngine.Mathf" preserve="all"/>
		<type fullname="UnityEngine.Debug" preserve="all"/>
		<type fullname="UnityEngine.ScriptableObject" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.AnimationModule">
	    <type fullname="UnityEngine.AnimationClip" preserve="all"/>
		<type fullname="UnityEngine.Motion" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.ParticleSystemModule">
	    <type fullname="UnityEngine.ParticleSystem" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.UnityWebRequestModule">
	    <type fullname="UnityEngine.Networking.UnityWebRequest" preserve="all"/>
		
	</assembly>

	<assembly fullname="hotfix">
	    <type fullname="mono.ui.utils.UIColorGroup" preserve="all"/>
		<type fullname="mono.ui.utils.UITween" preserve="all"/>
		<type fullname="mono.ui.space.RTEffect" preserve="all"/>
		<type fullname="mono.ui.space.RTSpace" preserve="all"/>
		<type fullname="mono.ui.joystick.SkillControl" preserve="all"/>
		<type fullname="mono.ui.elements.AudioIndexer" preserve="all"/>
		<type fullname="mono.ui.elements.Bar" preserve="all"/>
		<type fullname="mono.ui.elements.Clickable" preserve="all"/>
		<type fullname="mono.ui.elements.Draggable" preserve="all"/>
		<type fullname="mono.ui.elements.Dropable" preserve="all"/>
		<type fullname="mono.ui.elements.GradientText" preserve="all"/>
		<type fullname="mono.ui.elements.GroupActive" preserve="all"/>
		<type fullname="mono.ui.elements.GroupBoolean" preserve="all"/>
		<type fullname="mono.ui.elements.GroupColor" preserve="all"/>
		<type fullname="mono.ui.elements.GroupFloat" preserve="all"/>
		<type fullname="mono.ui.elements.GroupIndexVisible" preserve="all"/>
		<type fullname="mono.ui.elements.GroupInt" preserve="all"/>
		<type fullname="mono.ui.elements.GroupString" preserve="all"/>
		<type fullname="mono.ui.elements.ImageBlendMode" preserve="all"/>
		<type fullname="mono.ui.elements.MovieClip" preserve="all"/>
		<type fullname="mono.ui.elements.MultiBlurMask" preserve="all"/>
		<type fullname="mono.ui.elements.NumberField" preserve="all"/>
		<type fullname="mono.ui.elements.OversizeImage" preserve="all"/>
		<type fullname="mono.ui.elements.PButton" preserve="all"/>
		<type fullname="mono.ui.elements.PDropdown" preserve="all"/>
		<type fullname="mono.ui.elements.PImage" preserve="all"/>
		<type fullname="mono.ui.elements.PolyImage" preserve="all"/>
		<type fullname="mono.ui.elements.PortraitSetting" preserve="all"/>
		<type fullname="mono.ui.elements.PRepeatImage" preserve="all"/>
		<type fullname="mono.ui.elements.Pressable" preserve="all"/>
		<type fullname="mono.ui.elements.PText" preserve="all"/>
		<type fullname="mono.ui.elements.RichText" preserve="all"/>
		<type fullname="mono.ui.elements.Star" preserve="all"/>
		<type fullname="mono.ui.elements.Tab" preserve="all"/>
		<type fullname="mono.ui.elements.TouchableRect" preserve="all"/>
		<type fullname="mono.ui.elements.WorldMap" preserve="all"/>
		<type fullname="mono.ui.controls.ComoboList" preserve="all"/>
		<type fullname="mono.ui.controls.UIBackground" preserve="all"/>
		<type fullname="mono.ui.controls.UIBar" preserve="all"/>
		<type fullname="mono.ui.controls.UIButton" preserve="all"/>
		<type fullname="mono.ui.controls.UICheckBox" preserve="all"/>
		<type fullname="mono.ui.controls.UICombobox" preserve="all"/>
		<type fullname="mono.ui.controls.UIDataPane" preserve="all"/>
		<type fullname="mono.ui.controls.UIEffect" preserve="all"/>
		<type fullname="mono.ui.controls.UIItemPane" preserve="all"/>
		<type fullname="mono.ui.controls.UILabel" preserve="all"/>
		<type fullname="mono.ui.controls.UIPortrait" preserve="all"/>
		<type fullname="mono.ui.controls.UIPrefabReplace" preserve="all"/>
		<type fullname="mono.ui.controls.UIRadioButton" preserve="all"/>
		<type fullname="mono.ui.controls.UIResourceField" preserve="all"/>
		<type fullname="mono.ui.controls.UIRichText" preserve="all"/>
		<type fullname="mono.ui.controls.UISlider" preserve="all"/>
		<type fullname="mono.ui.controls.UIStar" preserve="all"/>
		<type fullname="mono.ui.controls.UITabNavigator" preserve="all"/>
		<type fullname="mono.ui.controls.UITextArea" preserve="all"/>
		<type fullname="mono.ui.controls.UITextInput" preserve="all"/>
		<type fullname="mono.scene.NpcRegion" preserve="all"/>
		<type fullname="mono.scene.Region" preserve="all"/>
		<type fullname="mono.scene.Teleport" preserve="all"/>
		<type fullname="mono.data.NpcFuncBBDelegate" preserve="all"/>
		<type fullname="mono.data.NpcFuncSell" preserve="all"/>
		<type fullname="mono.data.NpcFuncShop" preserve="all"/>
		<type fullname="mono.data.NpcFuncWash" preserve="all"/>
		<type fullname="mono.data.RebirthArea" preserve="all"/>
		<type fullname="dl.UI.AnimatorController" preserve="all"/>
		<type fullname="dl.UI.BossBlood" preserve="all"/>
		<type fullname="dl.UI.BossBloodSprite" preserve="all"/>
		<type fullname="dl.UI.EventGroup" preserve="all"/>
		<type fullname="dl.sequence.battle.BattleController" preserve="all"/>
		<type fullname="dl.runtime.DeviceInfo" preserve="all"/>
		<type fullname="dl.mono.ui.SeqImage" preserve="all"/>
		<type fullname="dl.lua.BridgeLuaScene" preserve="all"/>
		<type fullname="dl.entity.impl.EntityVisibiltyManager" preserve="all"/>
		<type fullname="dl.sequence.ui.SeqImage_Dll" preserve="all"/>
		
	</assembly>

	<assembly fullname="game">
	    <type fullname="game.ui.FlyTextManager" preserve="all"/>
		<type fullname="game.ui.LogManager" preserve="all"/>
		<type fullname="game.ui.MapManager" preserve="all"/>
		<type fullname="game.ui.ItemPane_Dll" preserve="all"/>
		<type fullname="game.ui.ListAction" preserve="all"/>
		<type fullname="game.ui.ListEvent" preserve="all"/>
		<type fullname="game.ui.ScrollVO" preserve="all"/>
		<type fullname="game.ui.UIDataPane_Dll" preserve="all"/>
		<type fullname="game.ui.RichEmoFactory" preserve="all"/>
		<type fullname="game.ui.MapIcon_Lua" preserve="all"/>
		<type fullname="game.ui.SelectableItemBase" preserve="all"/>
		<type fullname="game.ui.UIRendererBase" preserve="all"/>
		<type fullname="game.mono.PDropable_Dll" preserve="all"/>
		<type fullname="game.mono.range.SightData" preserve="all"/>
		<type fullname="game.mono.manager.SceneSpecialMode" preserve="all"/>
		<type fullname="game.mono.manager.SceneManager" preserve="all"/>
		<type fullname="game.mono.data.npc.NpcItem" preserve="all"/>
		<type fullname="game.mono.avatar.AvatarCondition" preserve="all"/>
		<type fullname="game.ai.entity.LuaEntity" preserve="all"/>
		<type fullname="game.ai.entity.ModelEntity" preserve="all"/>
		<type fullname="game.ai.entity.OperationCode" preserve="all"/>
		<type fullname="game.ai.entity.OperationVO" preserve="all"/>
		<type fullname="game.ai.entity.avatar.RenderPartPrefab" preserve="all"/>
		<type fullname="game.ai.entity.avatar.RenderSlot" preserve="all"/>
		<type fullname="game.ai.personality.NpcPropertyUnit" preserve="all"/>
		<type fullname="game.ai.machine.action.htask.HostTaskManager" preserve="all"/>
		<type fullname="game.ai.machine.action.htask.PathGuideManager" preserve="all"/>
		<type fullname="game.ai.machine.vo.AnimVO" preserve="all"/>
		<type fullname="game.ai.machine.vo.DamageCalc" preserve="all"/>
		<type fullname="game.ai.machine.vo.SkillCeoffModifier" preserve="all"/>
		<type fullname="game.ai.machine.vo.SkillRetrieverInfo" preserve="all"/>
		<type fullname="game.ai.machine.vo.FxBuffVO" preserve="all"/>
		<type fullname="game.ai.machine.vo.ComboVO" preserve="all"/>
		<type fullname="game.ai.machine.vo.FightResult" preserve="all"/>
		<type fullname="game.ai.client.Action_AIMode" preserve="all"/>
		<type fullname="game.ai.client.Action_Animation" preserve="all"/>
		<type fullname="game.ai.client.Action_AutoMode" preserve="all"/>
		<type fullname="game.ai.client.Action_BuffControl" preserve="all"/>
		<type fullname="game.ai.client.Action_BuffShow" preserve="all"/>
		<type fullname="game.ai.client.Action_BulletAdd" preserve="all"/>
		<type fullname="game.ai.client.Action_BulletTransform" preserve="all"/>
		<type fullname="game.ai.client.Action_BulletBind" preserve="all"/>
		<type fullname="game.ai.client.Action_IOLock" preserve="all"/>
		<type fullname="game.ai.client.Action_RemoveEntity" preserve="all"/>
		<type fullname="game.ai.client.Action_ShaderSlot" preserve="all"/>
		<type fullname="game.ai.client.Action_SkillLocate" preserve="all"/>
		<type fullname="game.ai.client.Action_SpeakBubble" preserve="all"/>
		<type fullname="game.ai.client.Action_SpeedMulti" preserve="all"/>
		<type fullname="game.ai.client.Action_Transform" preserve="all"/>
		<type fullname="game.tutor.ArrowQueue" preserve="all"/>
		<type fullname="game.tutor.TutorCenter" preserve="all"/>
		<type fullname="game.mediator.BaseMediator" preserve="all"/>
		<type fullname="game.mediator.ChildMediator" preserve="all"/>
		<type fullname="game.mediator.CoverMediator" preserve="all"/>
		<type fullname="game.mediator.FreeMediator" preserve="all"/>
		<type fullname="game.mediator.MediatorCore" preserve="all"/>
		<type fullname="game.mediator.PanelManager" preserve="all"/>
		<type fullname="game.mediator.SimpleView" preserve="all"/>
		<type fullname="game.mediator.TempMediator" preserve="all"/>
		<type fullname="game.mediator.setting.PanelCellType" preserve="all"/>
		<type fullname="game.manager.EntityManager" preserve="all"/>
		<type fullname="game.manager.SoundManager" preserve="all"/>
		<type fullname="game.lua.BridgeCallAsset" preserve="all"/>
		<type fullname="game.lua.BridgeCallBuff" preserve="all"/>
		<type fullname="game.lua.BridgeCallEntity" preserve="all"/>
		<type fullname="game.lua.BridgeCallSearch" preserve="all"/>
		<type fullname="game.lua.StationManager" preserve="all"/>
		<type fullname="game.lua.WorldBossCreator_Template" preserve="all"/>
		<type fullname="game.lua.BridgeLuaMissionManager" preserve="all"/>
		<type fullname="game.entry.VersionRotateBridge" preserve="all"/>
		<type fullname="game.ui.RTEffect_Dll" preserve="all"/>
		<type fullname="game.ui.RTSpace_Dll" preserve="all"/>
		<type fullname="game.ui.skill.SkillControl_Dll" preserve="all"/>
		<type fullname="game.ui.AudioIndexer_Dll" preserve="all"/>
		<type fullname="game.mono.Draggable_Dll" preserve="all"/>
		<type fullname="game.ui.GroupActive_Dll" preserve="all"/>
		<type fullname="game.ui.GroupValue_Dll`2[System.Int32,System.Boolean]" preserve="all"/>
		<type fullname="game.ui.ArrayValue_Dll`1[System.Boolean]" preserve="all"/>
		<type fullname="game.ui.ArrayValue_Dll`1[UnityEngine.Color]" preserve="all"/>
		<type fullname="game.ui.GroupValue_Dll`2[System.Int32,UnityEngine.Color]" preserve="all"/>
		<type fullname="game.ui.ArrayValue_Dll`1[System.Single]" preserve="all"/>
		<type fullname="game.ui.GroupValue_Dll`2[System.Int32,System.Single]" preserve="all"/>
		<type fullname="game.ui.GroupIndexVisible_Dll" preserve="all"/>
		<type fullname="game.ui.ArrayValue_Dll`1[System.Int32]" preserve="all"/>
		<type fullname="game.ui.GroupValue_Dll`2[System.Int32,System.Int32]" preserve="all"/>
		<type fullname="game.ui.ArrayValue_Dll`1[System.String]" preserve="all"/>
		<type fullname="game.ui.GroupValue_Dll`2[System.Int32,System.String]" preserve="all"/>
		<type fullname="game.ui.MultiBlurMask_Dll" preserve="all"/>
		<type fullname="game.ui.PortraitSetting_Dll" preserve="all"/>
		<type fullname="game.ui.PRichText_Dll" preserve="all"/>
		<type fullname="game.ui.WorldMap_Dll" preserve="all"/>
		<type fullname="game.ui.ComboList_Dll" preserve="all"/>
		<type fullname="game.ui.UIEffect_Dll" preserve="all"/>
		<type fullname="game.ui.UIPortrait_Dll" preserve="all"/>
		<type fullname="game.ui.UIRichText_Dll" preserve="all"/>
		<type fullname="game.ui.UITextArea_Dll" preserve="all"/>
		<type fullname="game.ui.BaseScrollPane" preserve="all"/>
		<type fullname="game.mono.data.npc.NpcRegion_Dll" preserve="all"/>
		<type fullname="game.mono.mono.AwakableMono" preserve="all"/>
		<type fullname="game.mono.mono.Region_Dll" preserve="all"/>
		<type fullname="game.mono.mono.Teleport_Dll" preserve="all"/>
		<type fullname="game.mono.data.npc.NpcFuncData" preserve="all"/>
		<type fullname="game.mono.data.npc.NpcFuncShop_Dll" preserve="all"/>
		<type fullname="game.mono.data.RebirthArea_Dll" preserve="all"/>
		<type fullname="game.ui.ItemPaneCore" preserve="all"/>
		<type fullname="game.ui.BaseDataPane" preserve="all"/>
		<type fullname="game.ui.MapIconBase" preserve="all"/>
		<type fullname="game.mediator.GameMediator" preserve="all"/>
		
	</assembly>

	<assembly fullname="pure">
	    <type fullname="pure.utils.tween.EasePathType" preserve="all"/>
		<type fullname="pure.utils.tween.TweenTargetMode" preserve="all"/>
		<type fullname="pure.utils.tween.EaseType" preserve="all"/>
		<type fullname="pure.utils.tween.TweenCore" preserve="all"/>
		<type fullname="pure.utils.tween.TweenData" preserve="all"/>
		<type fullname="pure.utils.tween.TweenParamater" preserve="all"/>
		<type fullname="pure.utils.tween.TweenDirection" preserve="all"/>
		<type fullname="pure.utils.tween.TweenGroup" preserve="all"/>
		<type fullname="pure.utils.tween.TweenGroupMode" preserve="all"/>
		<type fullname="pure.utils.tween.TweenState" preserve="all"/>
		<type fullname="pure.utils.tween.TweenWait" preserve="all"/>
		<type fullname="pure.utils.tween.extension.TweenComponentExtensions" preserve="all"/>
		<type fullname="pure.utils.tween.extension.TweenRectTransformExtentions" preserve="all"/>
		<type fullname="pure.utils.memory.ReportAsset" preserve="all"/>
		<type fullname="pure.utils.mathTools.HashCode" preserve="all"/>
		<type fullname="pure.utils.input.ButtonStatus" preserve="all"/>
		<type fullname="pure.utils.fileTools.Encruption" preserve="all"/>
		<type fullname="pure.utils.caculation.Formular" preserve="all"/>
		<type fullname="pure.ui.RectTransformExtensions" preserve="all"/>
		<type fullname="pure.ui.DataProvider" preserve="all"/>
		<type fullname="pure.ui.PBar_Dll" preserve="all"/>
		<type fullname="pure.ui.PButton_Dll" preserve="all"/>
		<type fullname="pure.ui.PDropDown_Dll" preserve="all"/>
		<type fullname="pure.ui.PGradientText_Dll" preserve="all"/>
		<type fullname="pure.ui.PIcon_Dll" preserve="all"/>
		<type fullname="pure.ui.MovieEvent" preserve="all"/>
		<type fullname="pure.ui.PMovieClip_Dll" preserve="all"/>
		<type fullname="pure.ui.PMoviePlayer_Dll" preserve="all"/>
		<type fullname="pure.ui.PPanel_Dll" preserve="all"/>
		<type fullname="pure.ui.PPressable_Dll" preserve="all"/>
		<type fullname="pure.ui.PRepeatImage_Dll" preserve="all"/>
		<type fullname="pure.ui.PStar_Dll" preserve="all"/>
		<type fullname="pure.ui.PTab_Dll" preserve="all"/>
		<type fullname="pure.ui.PText_Dll" preserve="all"/>
		<type fullname="pure.ui.PToggleGroup_Dll" preserve="all"/>
		<type fullname="pure.ui.PToggle_Dll" preserve="all"/>
		<type fullname="pure.ui.PImage_Dll" preserve="all"/>
		<type fullname="pure.ui.PPolyImage_Dll" preserve="all"/>
		<type fullname="pure.ui.LangImage_Dll" preserve="all"/>
		<type fullname="pure.ui.UITweenParameter" preserve="all"/>
		<type fullname="pure.ui.UITweenData" preserve="all"/>
		<type fullname="pure.ui.NumberField_Dll" preserve="all"/>
		<type fullname="pure.stateMachine.machine.RpxLuaContext" preserve="all"/>
		<type fullname="pure.stateMachine.machine.TransitionContext" preserve="all"/>
		<type fullname="pure.net.socket.LuaHttp" preserve="all"/>
		<type fullname="pure.net.socket.LuaServer" preserve="all"/>
		<type fullname="pure.ticker.SystemTime" preserve="all"/>
		<type fullname="pure.runtime.LocalData" preserve="all"/>
		<type fullname="pure.record.RecordManager" preserve="all"/>
		<type fullname="pure.native.NativeManager" preserve="all"/>
		<type fullname="pure.database.config.ProtoVerion_Dll" preserve="all"/>
		<type fullname="pure.cpp.CppSensitive" preserve="all"/>
		<type fullname="pure.assetdb.VersionData" preserve="all"/>
		<type fullname="pure.assetdb.VersionGroup" preserve="all"/>
		<type fullname="pure.assetdb.IconUtils" preserve="all"/>
		<type fullname="pure.ui.PRepeatImage_Dll+Direction" preserve="all"/>
		<type fullname="pure.ui.NumberField_Dll+NumberChangeMode" preserve="all"/>
		<type fullname="pure.database.config.ProtoVerion_Dll+History" preserve="all"/>
		<type fullname="pure.ui.UIColorGroup_Dll" preserve="all"/>
		<type fullname="pure.ui.UITween_Dll" preserve="all"/>
		<type fullname="pure.ui.PClickable_Dll" preserve="all"/>
		<type fullname="pure.ui.PImageBlendMode_Dll" preserve="all"/>
		<type fullname="pure.ui.OversizeImage_Dll" preserve="all"/>
		<type fullname="pure.ui.PTouchableRect_Dll" preserve="all"/>
		<type fullname="pure.ui.UIBackground_Dll" preserve="all"/>
		<type fullname="pure.ui.UICore" preserve="all"/>
		<type fullname="pure.ui.UIBar_Dll" preserve="all"/>
		<type fullname="pure.ui.UIButton_Dll" preserve="all"/>
		<type fullname="pure.ui.UIButtonCore" preserve="all"/>
		<type fullname="pure.ui.UICheckBox_Dll" preserve="all"/>
		<type fullname="pure.ui.UICombobox_Dll" preserve="all"/>
		<type fullname="pure.ui.UILabel_Dll" preserve="all"/>
		<type fullname="pure.ui.UIPrefabReplace_Dll" preserve="all"/>
		<type fullname="pure.ui.UIRadioButton_Dll" preserve="all"/>
		<type fullname="pure.ui.UIResourceField_Dll" preserve="all"/>
		<type fullname="pure.ui.UISlider_Dll" preserve="all"/>
		<type fullname="pure.ui.UIStar_Dll" preserve="all"/>
		<type fullname="pure.ui.UITabNavigagor_Dll" preserve="all"/>
		<type fullname="pure.ui.UITextInput_Dll" preserve="all"/>
		<type fullname="pure.stateMachine.core.ComplexEntity" preserve="all"/>
		<type fullname="pure.stateMachine.core.Entity" preserve="all"/>
		<type fullname="pure.stateMachine.machine.RpxContext" preserve="all"/>
		<type fullname="pure.behavior.BTContext" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.UI">
	    <type fullname="UnityEngine.EventSystems.UIBehaviour" preserve="all"/>
		<type fullname="UnityEngine.UI.Selectable" preserve="all"/>
		<type fullname="UnityEngine.UI.MaskableGraphic" preserve="all"/>
		<type fullname="UnityEngine.UI.Graphic" preserve="all"/>
		<type fullname="UnityEngine.UI.Text" preserve="all"/>
		<type fullname="UnityEngine.UI.Image" preserve="all"/>
		
	</assembly>

</linker>