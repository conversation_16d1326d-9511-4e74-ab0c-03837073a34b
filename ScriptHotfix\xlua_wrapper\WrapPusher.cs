﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using System;


namespace XLua{
    public  sealed class WrapPusher:ObjectTranslator {
    	public WrapPusher(LuaEnv luaenv, IntPtr L) : base(luaenv, L) {
        }
        
        class IniterAddergameuiScrollVO{
            static IniterAddergameuiScrollVO(){
                LuaEnv.AddIniter(Init);
            }

			static void Init(LuaEnv luaenv, ObjectTranslator translator){
				WrapPusher w = translator as WrapPusher;
				if (w == null) return;
			
				w.RegisterPushAndGetAndUpdate<game.ui.ScrollVO>(w.<PERSON><PERSON>, w.Get, w.UpdategameuiScrollVO);
				w.RegisterPushAndGetAndUpdate<game.mono.manager.SceneSpecialMode>(w.PushgamemonomanagerSceneSpecialMode, w.Get, w.UpdategamemonomanagerSceneSpecialMode);
				w.RegisterPushAndGetAndUpdate<game.mono.avatar.AvatarCondition>(w.PushgamemonoavatarAvatarCondition, w.Get, w.UpdategamemonoavatarAvatarCondition);
				w.RegisterPushAndGetAndUpdate<game.ai.entity.OperationCode>(w.PushgameaientityOperationCode, w.Get, w.UpdategameaientityOperationCode);
				w.RegisterPushAndGetAndUpdate<game.ai.entity.OperationVO>(w.PushgameaientityOperationVO, w.Get, w.UpdategameaientityOperationVO);
				w.RegisterPushAndGetAndUpdate<game.ai.entity.avatar.RenderSlot>(w.PushgameaientityavatarRenderSlot, w.Get, w.UpdategameaientityavatarRenderSlot);
				w.RegisterPushAndGetAndUpdate<game.ai.personality.NpcPropertyUnit>(w.PushgameaipersonalityNpcPropertyUnit, w.Get, w.UpdategameaipersonalityNpcPropertyUnit);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.AnimVO>(w.PushgameaimachinevoAnimVO, w.Get, w.UpdategameaimachinevoAnimVO);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.SkillCeoffModifier>(w.PushgameaimachinevoSkillCeoffModifier, w.Get, w.UpdategameaimachinevoSkillCeoffModifier);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.SkillRetrieverInfo>(w.PushgameaimachinevoSkillRetrieverInfo, w.Get, w.UpdategameaimachinevoSkillRetrieverInfo);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.FxBuffVO>(w.PushgameaimachinevoFxBuffVO, w.Get, w.UpdategameaimachinevoFxBuffVO);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.ComboVO>(w.PushgameaimachinevoComboVO, w.Get, w.UpdategameaimachinevoComboVO);
				w.RegisterPushAndGetAndUpdate<game.ai.machine.vo.FightResult>(w.PushgameaimachinevoFightResult, w.Get, w.UpdategameaimachinevoFightResult);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_AIMode>(w.PushgameaiclientAction_AIMode, w.Get, w.UpdategameaiclientAction_AIMode);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_Animation>(w.PushgameaiclientAction_Animation, w.Get, w.UpdategameaiclientAction_Animation);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_AutoMode>(w.PushgameaiclientAction_AutoMode, w.Get, w.UpdategameaiclientAction_AutoMode);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_BuffControl>(w.PushgameaiclientAction_BuffControl, w.Get, w.UpdategameaiclientAction_BuffControl);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_BuffShow>(w.PushgameaiclientAction_BuffShow, w.Get, w.UpdategameaiclientAction_BuffShow);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_BulletAdd>(w.PushgameaiclientAction_BulletAdd, w.Get, w.UpdategameaiclientAction_BulletAdd);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_BulletTransform>(w.PushgameaiclientAction_BulletTransform, w.Get, w.UpdategameaiclientAction_BulletTransform);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_BulletBind>(w.PushgameaiclientAction_BulletBind, w.Get, w.UpdategameaiclientAction_BulletBind);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_IOLock>(w.PushgameaiclientAction_IOLock, w.Get, w.UpdategameaiclientAction_IOLock);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_RemoveEntity>(w.PushgameaiclientAction_RemoveEntity, w.Get, w.UpdategameaiclientAction_RemoveEntity);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_ShaderSlot>(w.PushgameaiclientAction_ShaderSlot, w.Get, w.UpdategameaiclientAction_ShaderSlot);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_SkillLocate>(w.PushgameaiclientAction_SkillLocate, w.Get, w.UpdategameaiclientAction_SkillLocate);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_SpeakBubble>(w.PushgameaiclientAction_SpeakBubble, w.Get, w.UpdategameaiclientAction_SpeakBubble);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_SpeedMulti>(w.PushgameaiclientAction_SpeedMulti, w.Get, w.UpdategameaiclientAction_SpeedMulti);
				w.RegisterPushAndGetAndUpdate<game.ai.client.Action_Transform>(w.PushgameaiclientAction_Transform, w.Get, w.UpdategameaiclientAction_Transform);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.EaseType>(w.PushpureutilstweenEaseType, w.Get, w.UpdatepureutilstweenEaseType);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.TweenParamater>(w.PushpureutilstweenTweenParamater, w.Get, w.UpdatepureutilstweenTweenParamater);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.TweenGroupMode>(w.PushpureutilstweenTweenGroupMode, w.Get, w.UpdatepureutilstweenTweenGroupMode);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.TweenState>(w.PushpureutilstweenTweenState, w.Get, w.UpdatepureutilstweenTweenState);
				w.RegisterPushAndGetAndUpdate<pure.utils.mathTools.HashCode>(w.PushpureutilsmathToolsHashCode, w.Get, w.UpdatepureutilsmathToolsHashCode);
				w.RegisterPushAndGetAndUpdate<pure.utils.input.ButtonStatus>(w.PushpureutilsinputButtonStatus, w.Get, w.UpdatepureutilsinputButtonStatus);
				w.RegisterPushAndGetAndUpdate<pure.ui.MovieEvent>(w.PushpureuiMovieEvent, w.Get, w.UpdatepureuiMovieEvent);
				w.RegisterPushAndGetAndUpdate<pure.ui.UITweenParameter>(w.PushpureuiUITweenParameter, w.Get, w.UpdatepureuiUITweenParameter);
				w.RegisterPushAndGetAndUpdate<pure.ui.PRepeatImage_Dll.Direction>(w.PushpureuiPRepeatImage_DllDirection, w.Get, w.UpdatepureuiPRepeatImage_DllDirection);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Vector2>(w.PushUnityEngineVector2, w.Get, w.UpdateUnityEngineVector2);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Vector3>(w.PushUnityEngineVector3, w.Get, w.UpdateUnityEngineVector3);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Vector4>(w.PushUnityEngineVector4, w.Get, w.UpdateUnityEngineVector4);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Color>(w.PushUnityEngineColor, w.Get, w.UpdateUnityEngineColor);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Quaternion>(w.PushUnityEngineQuaternion, w.Get, w.UpdateUnityEngineQuaternion);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Ray>(w.PushUnityEngineRay, w.Get, w.UpdateUnityEngineRay);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Bounds>(w.PushUnityEngineBounds, w.Get, w.UpdateUnityEngineBounds);
				w.RegisterPushAndGetAndUpdate<UnityEngine.Ray2D>(w.PushUnityEngineRay2D, w.Get, w.UpdateUnityEngineRay2D);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.EasePathType>(w.PushpureutilstweenEasePathType, w.Get, w.UpdatepureutilstweenEasePathType);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.TweenTargetMode>(w.PushpureutilstweenTweenTargetMode, w.Get, w.UpdatepureutilstweenTweenTargetMode);
				w.RegisterPushAndGetAndUpdate<pure.utils.tween.TweenDirection>(w.PushpureutilstweenTweenDirection, w.Get, w.UpdatepureutilstweenTweenDirection);
				w.RegisterPushAndGetAndUpdate<pure.ui.NumberField_Dll.NumberChangeMode>(w.PushpureuiNumberField_DllNumberChangeMode, w.Get, w.UpdatepureuiNumberField_DllNumberChangeMode);
			
				w.RegisterCaster<game.mono.range.SightData>(w.Get);
				w.RegisterCaster<game.ai.machine.vo.DamageCalc>(w.Get);
			}
        }

        static IniterAddergameuiScrollVO s_IniterAddergameuiScrollVO_dumb_obj = new IniterAddergameuiScrollVO();
        static IniterAddergameuiScrollVO IniterAddergameuiScrollVO_dumb_obj {get{return s_IniterAddergameuiScrollVO_dumb_obj;}}
        

  		int gameuiScrollVO_TypeID = -1;
        public void PushgameuiScrollVO(RealStatePtr L, game.ui.ScrollVO val){
        	if (gameuiScrollVO_TypeID == -1)
            {
			    bool is_first;
                gameuiScrollVO_TypeID = getTypeId(L, typeof(game.ui.ScrollVO), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, gameuiScrollVO_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ui.ScrollVO ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ui.ScrollVO val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameuiScrollVO_TypeID){
				    throw new Exception("invalid userdata for game.ui.ScrollVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ui.ScrollVO");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ui.ScrollVO)objectCasters.GetCaster(typeof(game.ui.ScrollVO))(L, index, null);
            }
        }

        public void UpdategameuiScrollVO(RealStatePtr L, int index, game.ui.ScrollVO val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameuiScrollVO_TypeID){
				    throw new Exception("invalid userdata for game.ui.ScrollVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ui.ScrollVO ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gamemonomanagerSceneSpecialMode_TypeID = -1;
		int gamemonomanagerSceneSpecialMode_EnumRef = -1;
        
        public void PushgamemonomanagerSceneSpecialMode(RealStatePtr L, game.mono.manager.SceneSpecialMode val){
        	if (gamemonomanagerSceneSpecialMode_TypeID == -1)
            {
			    bool is_first;
                gamemonomanagerSceneSpecialMode_TypeID = getTypeId(L, typeof(game.mono.manager.SceneSpecialMode), out is_first);
				
				if (gamemonomanagerSceneSpecialMode_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(game.mono.manager.SceneSpecialMode));
				    gamemonomanagerSceneSpecialMode_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, gamemonomanagerSceneSpecialMode_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, gamemonomanagerSceneSpecialMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for game.mono.manager.SceneSpecialMode ,value="+val);
            }
			
			LuaAPI.lua_getref(L, gamemonomanagerSceneSpecialMode_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out game.mono.manager.SceneSpecialMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gamemonomanagerSceneSpecialMode_TypeID){
				    throw new Exception("invalid userdata for game.mono.manager.SceneSpecialMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for game.mono.manager.SceneSpecialMode");
                }
				val = (game.mono.manager.SceneSpecialMode)e;
                
            }
            else{
                val = (game.mono.manager.SceneSpecialMode)objectCasters.GetCaster(typeof(game.mono.manager.SceneSpecialMode))(L, index, null);
            }
        }

        public void UpdategamemonomanagerSceneSpecialMode(RealStatePtr L, int index, game.mono.manager.SceneSpecialMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gamemonomanagerSceneSpecialMode_TypeID){
				    throw new Exception("invalid userdata for game.mono.manager.SceneSpecialMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for game.mono.manager.SceneSpecialMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gamemonoavatarAvatarCondition_TypeID = -1;
        public void PushgamemonoavatarAvatarCondition(RealStatePtr L, game.mono.avatar.AvatarCondition val){
        	if (gamemonoavatarAvatarCondition_TypeID == -1)
            {
			    bool is_first;
                gamemonoavatarAvatarCondition_TypeID = getTypeId(L, typeof(game.mono.avatar.AvatarCondition), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, gamemonoavatarAvatarCondition_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.mono.avatar.AvatarCondition ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.mono.avatar.AvatarCondition val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gamemonoavatarAvatarCondition_TypeID){
				    throw new Exception("invalid userdata for game.mono.avatar.AvatarCondition");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.mono.avatar.AvatarCondition");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.mono.avatar.AvatarCondition)objectCasters.GetCaster(typeof(game.mono.avatar.AvatarCondition))(L, index, null);
            }
        }

        public void UpdategamemonoavatarAvatarCondition(RealStatePtr L, int index, game.mono.avatar.AvatarCondition val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gamemonoavatarAvatarCondition_TypeID){
				    throw new Exception("invalid userdata for game.mono.avatar.AvatarCondition");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.mono.avatar.AvatarCondition ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaientityOperationCode_TypeID = -1;
		int gameaientityOperationCode_EnumRef = -1;
        
        public void PushgameaientityOperationCode(RealStatePtr L, game.ai.entity.OperationCode val){
        	if (gameaientityOperationCode_TypeID == -1)
            {
			    bool is_first;
                gameaientityOperationCode_TypeID = getTypeId(L, typeof(game.ai.entity.OperationCode), out is_first);
				
				if (gameaientityOperationCode_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(game.ai.entity.OperationCode));
				    gameaientityOperationCode_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, gameaientityOperationCode_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, gameaientityOperationCode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for game.ai.entity.OperationCode ,value="+val);
            }
			
			LuaAPI.lua_getref(L, gameaientityOperationCode_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.entity.OperationCode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityOperationCode_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.OperationCode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for game.ai.entity.OperationCode");
                }
				val = (game.ai.entity.OperationCode)e;
                
            }
            else{
                val = (game.ai.entity.OperationCode)objectCasters.GetCaster(typeof(game.ai.entity.OperationCode))(L, index, null);
            }
        }

        public void UpdategameaientityOperationCode(RealStatePtr L, int index, game.ai.entity.OperationCode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityOperationCode_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.OperationCode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for game.ai.entity.OperationCode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaientityOperationVO_TypeID = -1;
        public void PushgameaientityOperationVO(RealStatePtr L, game.ai.entity.OperationVO val){
        	if (gameaientityOperationVO_TypeID == -1)
            {
			    bool is_first;
                gameaientityOperationVO_TypeID = getTypeId(L, typeof(game.ai.entity.OperationVO), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 32, gameaientityOperationVO_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.entity.OperationVO ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.entity.OperationVO val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityOperationVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.OperationVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.entity.OperationVO");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.entity.OperationVO)objectCasters.GetCaster(typeof(game.ai.entity.OperationVO))(L, index, null);
            }
        }

        public void UpdategameaientityOperationVO(RealStatePtr L, int index, game.ai.entity.OperationVO val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityOperationVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.OperationVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.entity.OperationVO ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaientityavatarRenderSlot_TypeID = -1;
        public void PushgameaientityavatarRenderSlot(RealStatePtr L, game.ai.entity.avatar.RenderSlot val){
        	if (gameaientityavatarRenderSlot_TypeID == -1)
            {
			    bool is_first;
                gameaientityavatarRenderSlot_TypeID = getTypeId(L, typeof(game.ai.entity.avatar.RenderSlot), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 32, gameaientityavatarRenderSlot_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.entity.avatar.RenderSlot ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.entity.avatar.RenderSlot val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityavatarRenderSlot_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.avatar.RenderSlot");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.entity.avatar.RenderSlot");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.entity.avatar.RenderSlot)objectCasters.GetCaster(typeof(game.ai.entity.avatar.RenderSlot))(L, index, null);
            }
        }

        public void UpdategameaientityavatarRenderSlot(RealStatePtr L, int index, game.ai.entity.avatar.RenderSlot val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaientityavatarRenderSlot_TypeID){
				    throw new Exception("invalid userdata for game.ai.entity.avatar.RenderSlot");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.entity.avatar.RenderSlot ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaipersonalityNpcPropertyUnit_TypeID = -1;
        public void PushgameaipersonalityNpcPropertyUnit(RealStatePtr L, game.ai.personality.NpcPropertyUnit val){
        	if (gameaipersonalityNpcPropertyUnit_TypeID == -1)
            {
			    bool is_first;
                gameaipersonalityNpcPropertyUnit_TypeID = getTypeId(L, typeof(game.ai.personality.NpcPropertyUnit), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, gameaipersonalityNpcPropertyUnit_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.personality.NpcPropertyUnit ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.personality.NpcPropertyUnit val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaipersonalityNpcPropertyUnit_TypeID){
				    throw new Exception("invalid userdata for game.ai.personality.NpcPropertyUnit");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.personality.NpcPropertyUnit");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.personality.NpcPropertyUnit)objectCasters.GetCaster(typeof(game.ai.personality.NpcPropertyUnit))(L, index, null);
            }
        }

        public void UpdategameaipersonalityNpcPropertyUnit(RealStatePtr L, int index, game.ai.personality.NpcPropertyUnit val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaipersonalityNpcPropertyUnit_TypeID){
				    throw new Exception("invalid userdata for game.ai.personality.NpcPropertyUnit");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.personality.NpcPropertyUnit ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoAnimVO_TypeID = -1;
        public void PushgameaimachinevoAnimVO(RealStatePtr L, game.ai.machine.vo.AnimVO val){
        	if (gameaimachinevoAnimVO_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoAnimVO_TypeID = getTypeId(L, typeof(game.ai.machine.vo.AnimVO), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, gameaimachinevoAnimVO_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.AnimVO ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.AnimVO val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoAnimVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.AnimVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.AnimVO");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.AnimVO)objectCasters.GetCaster(typeof(game.ai.machine.vo.AnimVO))(L, index, null);
            }
        }

        public void UpdategameaimachinevoAnimVO(RealStatePtr L, int index, game.ai.machine.vo.AnimVO val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoAnimVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.AnimVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.AnimVO ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoSkillCeoffModifier_TypeID = -1;
        public void PushgameaimachinevoSkillCeoffModifier(RealStatePtr L, game.ai.machine.vo.SkillCeoffModifier val){
        	if (gameaimachinevoSkillCeoffModifier_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoSkillCeoffModifier_TypeID = getTypeId(L, typeof(game.ai.machine.vo.SkillCeoffModifier), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, gameaimachinevoSkillCeoffModifier_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.SkillCeoffModifier ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.SkillCeoffModifier val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoSkillCeoffModifier_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.SkillCeoffModifier");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.SkillCeoffModifier");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.SkillCeoffModifier)objectCasters.GetCaster(typeof(game.ai.machine.vo.SkillCeoffModifier))(L, index, null);
            }
        }

        public void UpdategameaimachinevoSkillCeoffModifier(RealStatePtr L, int index, game.ai.machine.vo.SkillCeoffModifier val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoSkillCeoffModifier_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.SkillCeoffModifier");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.SkillCeoffModifier ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoSkillRetrieverInfo_TypeID = -1;
        public void PushgameaimachinevoSkillRetrieverInfo(RealStatePtr L, game.ai.machine.vo.SkillRetrieverInfo val){
        	if (gameaimachinevoSkillRetrieverInfo_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoSkillRetrieverInfo_TypeID = getTypeId(L, typeof(game.ai.machine.vo.SkillRetrieverInfo), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 28, gameaimachinevoSkillRetrieverInfo_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.SkillRetrieverInfo ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.SkillRetrieverInfo val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoSkillRetrieverInfo_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.SkillRetrieverInfo");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.SkillRetrieverInfo");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.SkillRetrieverInfo)objectCasters.GetCaster(typeof(game.ai.machine.vo.SkillRetrieverInfo))(L, index, null);
            }
        }

        public void UpdategameaimachinevoSkillRetrieverInfo(RealStatePtr L, int index, game.ai.machine.vo.SkillRetrieverInfo val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoSkillRetrieverInfo_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.SkillRetrieverInfo");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.SkillRetrieverInfo ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoFxBuffVO_TypeID = -1;
        public void PushgameaimachinevoFxBuffVO(RealStatePtr L, game.ai.machine.vo.FxBuffVO val){
        	if (gameaimachinevoFxBuffVO_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoFxBuffVO_TypeID = getTypeId(L, typeof(game.ai.machine.vo.FxBuffVO), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 28, gameaimachinevoFxBuffVO_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.FxBuffVO ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.FxBuffVO val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoFxBuffVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.FxBuffVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.FxBuffVO");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.FxBuffVO)objectCasters.GetCaster(typeof(game.ai.machine.vo.FxBuffVO))(L, index, null);
            }
        }

        public void UpdategameaimachinevoFxBuffVO(RealStatePtr L, int index, game.ai.machine.vo.FxBuffVO val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoFxBuffVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.FxBuffVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.FxBuffVO ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoComboVO_TypeID = -1;
        public void PushgameaimachinevoComboVO(RealStatePtr L, game.ai.machine.vo.ComboVO val){
        	if (gameaimachinevoComboVO_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoComboVO_TypeID = getTypeId(L, typeof(game.ai.machine.vo.ComboVO), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, gameaimachinevoComboVO_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.ComboVO ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.ComboVO val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoComboVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.ComboVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.ComboVO");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.ComboVO)objectCasters.GetCaster(typeof(game.ai.machine.vo.ComboVO))(L, index, null);
            }
        }

        public void UpdategameaimachinevoComboVO(RealStatePtr L, int index, game.ai.machine.vo.ComboVO val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoComboVO_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.ComboVO");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.ComboVO ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaimachinevoFightResult_TypeID = -1;
        public void PushgameaimachinevoFightResult(RealStatePtr L, game.ai.machine.vo.FightResult val){
        	if (gameaimachinevoFightResult_TypeID == -1)
            {
			    bool is_first;
                gameaimachinevoFightResult_TypeID = getTypeId(L, typeof(game.ai.machine.vo.FightResult), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, gameaimachinevoFightResult_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.machine.vo.FightResult ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.machine.vo.FightResult val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoFightResult_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.FightResult");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.machine.vo.FightResult");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.machine.vo.FightResult)objectCasters.GetCaster(typeof(game.ai.machine.vo.FightResult))(L, index, null);
            }
        }

        public void UpdategameaimachinevoFightResult(RealStatePtr L, int index, game.ai.machine.vo.FightResult val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaimachinevoFightResult_TypeID){
				    throw new Exception("invalid userdata for game.ai.machine.vo.FightResult");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.machine.vo.FightResult ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_AIMode_TypeID = -1;
        public void PushgameaiclientAction_AIMode(RealStatePtr L, game.ai.client.Action_AIMode val){
        	if (gameaiclientAction_AIMode_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_AIMode_TypeID = getTypeId(L, typeof(game.ai.client.Action_AIMode), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 28, gameaiclientAction_AIMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_AIMode ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_AIMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_AIMode_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_AIMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_AIMode");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_AIMode)objectCasters.GetCaster(typeof(game.ai.client.Action_AIMode))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_AIMode(RealStatePtr L, int index, game.ai.client.Action_AIMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_AIMode_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_AIMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_AIMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_Animation_TypeID = -1;
        public void PushgameaiclientAction_Animation(RealStatePtr L, game.ai.client.Action_Animation val){
        	if (gameaiclientAction_Animation_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_Animation_TypeID = getTypeId(L, typeof(game.ai.client.Action_Animation), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 36, gameaiclientAction_Animation_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_Animation ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_Animation val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_Animation_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_Animation");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_Animation");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_Animation)objectCasters.GetCaster(typeof(game.ai.client.Action_Animation))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_Animation(RealStatePtr L, int index, game.ai.client.Action_Animation val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_Animation_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_Animation");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_Animation ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_AutoMode_TypeID = -1;
        public void PushgameaiclientAction_AutoMode(RealStatePtr L, game.ai.client.Action_AutoMode val){
        	if (gameaiclientAction_AutoMode_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_AutoMode_TypeID = getTypeId(L, typeof(game.ai.client.Action_AutoMode), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 12, gameaiclientAction_AutoMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_AutoMode ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_AutoMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_AutoMode_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_AutoMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_AutoMode");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_AutoMode)objectCasters.GetCaster(typeof(game.ai.client.Action_AutoMode))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_AutoMode(RealStatePtr L, int index, game.ai.client.Action_AutoMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_AutoMode_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_AutoMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_AutoMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_BuffControl_TypeID = -1;
        public void PushgameaiclientAction_BuffControl(RealStatePtr L, game.ai.client.Action_BuffControl val){
        	if (gameaiclientAction_BuffControl_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_BuffControl_TypeID = getTypeId(L, typeof(game.ai.client.Action_BuffControl), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 12, gameaiclientAction_BuffControl_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_BuffControl ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_BuffControl val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BuffControl_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BuffControl");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_BuffControl");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_BuffControl)objectCasters.GetCaster(typeof(game.ai.client.Action_BuffControl))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_BuffControl(RealStatePtr L, int index, game.ai.client.Action_BuffControl val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BuffControl_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BuffControl");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_BuffControl ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_BuffShow_TypeID = -1;
        public void PushgameaiclientAction_BuffShow(RealStatePtr L, game.ai.client.Action_BuffShow val){
        	if (gameaiclientAction_BuffShow_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_BuffShow_TypeID = getTypeId(L, typeof(game.ai.client.Action_BuffShow), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 52, gameaiclientAction_BuffShow_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_BuffShow ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_BuffShow val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BuffShow_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BuffShow");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_BuffShow");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_BuffShow)objectCasters.GetCaster(typeof(game.ai.client.Action_BuffShow))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_BuffShow(RealStatePtr L, int index, game.ai.client.Action_BuffShow val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BuffShow_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BuffShow");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_BuffShow ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_BulletAdd_TypeID = -1;
        public void PushgameaiclientAction_BulletAdd(RealStatePtr L, game.ai.client.Action_BulletAdd val){
        	if (gameaiclientAction_BulletAdd_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_BulletAdd_TypeID = getTypeId(L, typeof(game.ai.client.Action_BulletAdd), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 68, gameaiclientAction_BulletAdd_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_BulletAdd ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_BulletAdd val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletAdd_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletAdd");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_BulletAdd");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_BulletAdd)objectCasters.GetCaster(typeof(game.ai.client.Action_BulletAdd))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_BulletAdd(RealStatePtr L, int index, game.ai.client.Action_BulletAdd val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletAdd_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletAdd");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_BulletAdd ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_BulletTransform_TypeID = -1;
        public void PushgameaiclientAction_BulletTransform(RealStatePtr L, game.ai.client.Action_BulletTransform val){
        	if (gameaiclientAction_BulletTransform_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_BulletTransform_TypeID = getTypeId(L, typeof(game.ai.client.Action_BulletTransform), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 40, gameaiclientAction_BulletTransform_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_BulletTransform ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_BulletTransform val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletTransform_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletTransform");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_BulletTransform");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_BulletTransform)objectCasters.GetCaster(typeof(game.ai.client.Action_BulletTransform))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_BulletTransform(RealStatePtr L, int index, game.ai.client.Action_BulletTransform val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletTransform_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletTransform");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_BulletTransform ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_BulletBind_TypeID = -1;
        public void PushgameaiclientAction_BulletBind(RealStatePtr L, game.ai.client.Action_BulletBind val){
        	if (gameaiclientAction_BulletBind_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_BulletBind_TypeID = getTypeId(L, typeof(game.ai.client.Action_BulletBind), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 48, gameaiclientAction_BulletBind_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_BulletBind ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_BulletBind val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletBind_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletBind");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_BulletBind");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_BulletBind)objectCasters.GetCaster(typeof(game.ai.client.Action_BulletBind))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_BulletBind(RealStatePtr L, int index, game.ai.client.Action_BulletBind val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_BulletBind_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_BulletBind");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_BulletBind ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_IOLock_TypeID = -1;
        public void PushgameaiclientAction_IOLock(RealStatePtr L, game.ai.client.Action_IOLock val){
        	if (gameaiclientAction_IOLock_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_IOLock_TypeID = getTypeId(L, typeof(game.ai.client.Action_IOLock), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 20, gameaiclientAction_IOLock_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_IOLock ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_IOLock val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_IOLock_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_IOLock");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_IOLock");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_IOLock)objectCasters.GetCaster(typeof(game.ai.client.Action_IOLock))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_IOLock(RealStatePtr L, int index, game.ai.client.Action_IOLock val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_IOLock_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_IOLock");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_IOLock ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_RemoveEntity_TypeID = -1;
        public void PushgameaiclientAction_RemoveEntity(RealStatePtr L, game.ai.client.Action_RemoveEntity val){
        	if (gameaiclientAction_RemoveEntity_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_RemoveEntity_TypeID = getTypeId(L, typeof(game.ai.client.Action_RemoveEntity), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 28, gameaiclientAction_RemoveEntity_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_RemoveEntity ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_RemoveEntity val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_RemoveEntity_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_RemoveEntity");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_RemoveEntity");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_RemoveEntity)objectCasters.GetCaster(typeof(game.ai.client.Action_RemoveEntity))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_RemoveEntity(RealStatePtr L, int index, game.ai.client.Action_RemoveEntity val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_RemoveEntity_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_RemoveEntity");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_RemoveEntity ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_ShaderSlot_TypeID = -1;
        public void PushgameaiclientAction_ShaderSlot(RealStatePtr L, game.ai.client.Action_ShaderSlot val){
        	if (gameaiclientAction_ShaderSlot_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_ShaderSlot_TypeID = getTypeId(L, typeof(game.ai.client.Action_ShaderSlot), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 40, gameaiclientAction_ShaderSlot_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_ShaderSlot ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_ShaderSlot val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_ShaderSlot_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_ShaderSlot");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_ShaderSlot");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_ShaderSlot)objectCasters.GetCaster(typeof(game.ai.client.Action_ShaderSlot))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_ShaderSlot(RealStatePtr L, int index, game.ai.client.Action_ShaderSlot val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_ShaderSlot_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_ShaderSlot");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_ShaderSlot ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_SkillLocate_TypeID = -1;
        public void PushgameaiclientAction_SkillLocate(RealStatePtr L, game.ai.client.Action_SkillLocate val){
        	if (gameaiclientAction_SkillLocate_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_SkillLocate_TypeID = getTypeId(L, typeof(game.ai.client.Action_SkillLocate), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 44, gameaiclientAction_SkillLocate_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_SkillLocate ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_SkillLocate val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SkillLocate_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SkillLocate");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_SkillLocate");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_SkillLocate)objectCasters.GetCaster(typeof(game.ai.client.Action_SkillLocate))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_SkillLocate(RealStatePtr L, int index, game.ai.client.Action_SkillLocate val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SkillLocate_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SkillLocate");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_SkillLocate ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_SpeakBubble_TypeID = -1;
        public void PushgameaiclientAction_SpeakBubble(RealStatePtr L, game.ai.client.Action_SpeakBubble val){
        	if (gameaiclientAction_SpeakBubble_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_SpeakBubble_TypeID = getTypeId(L, typeof(game.ai.client.Action_SpeakBubble), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 44, gameaiclientAction_SpeakBubble_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_SpeakBubble ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_SpeakBubble val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SpeakBubble_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SpeakBubble");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_SpeakBubble");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_SpeakBubble)objectCasters.GetCaster(typeof(game.ai.client.Action_SpeakBubble))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_SpeakBubble(RealStatePtr L, int index, game.ai.client.Action_SpeakBubble val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SpeakBubble_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SpeakBubble");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_SpeakBubble ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_SpeedMulti_TypeID = -1;
        public void PushgameaiclientAction_SpeedMulti(RealStatePtr L, game.ai.client.Action_SpeedMulti val){
        	if (gameaiclientAction_SpeedMulti_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_SpeedMulti_TypeID = getTypeId(L, typeof(game.ai.client.Action_SpeedMulti), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 12, gameaiclientAction_SpeedMulti_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_SpeedMulti ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_SpeedMulti val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SpeedMulti_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SpeedMulti");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_SpeedMulti");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_SpeedMulti)objectCasters.GetCaster(typeof(game.ai.client.Action_SpeedMulti))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_SpeedMulti(RealStatePtr L, int index, game.ai.client.Action_SpeedMulti val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_SpeedMulti_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_SpeedMulti");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_SpeedMulti ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int gameaiclientAction_Transform_TypeID = -1;
        public void PushgameaiclientAction_Transform(RealStatePtr L, game.ai.client.Action_Transform val){
        	if (gameaiclientAction_Transform_TypeID == -1)
            {
			    bool is_first;
                gameaiclientAction_Transform_TypeID = getTypeId(L, typeof(game.ai.client.Action_Transform), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 44, gameaiclientAction_Transform_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for game.ai.client.Action_Transform ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out game.ai.client.Action_Transform val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_Transform_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_Transform");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for game.ai.client.Action_Transform");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (game.ai.client.Action_Transform)objectCasters.GetCaster(typeof(game.ai.client.Action_Transform))(L, index, null);
            }
        }

        public void UpdategameaiclientAction_Transform(RealStatePtr L, int index, game.ai.client.Action_Transform val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != gameaiclientAction_Transform_TypeID){
				    throw new Exception("invalid userdata for game.ai.client.Action_Transform");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for game.ai.client.Action_Transform ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenEaseType_TypeID = -1;
		int pureutilstweenEaseType_EnumRef = -1;
        
        public void PushpureutilstweenEaseType(RealStatePtr L, pure.utils.tween.EaseType val){
        	if (pureutilstweenEaseType_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenEaseType_TypeID = getTypeId(L, typeof(pure.utils.tween.EaseType), out is_first);
				
				if (pureutilstweenEaseType_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.EaseType));
				    pureutilstweenEaseType_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenEaseType_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenEaseType_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.EaseType ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenEaseType_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.EaseType val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenEaseType_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.EaseType");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.EaseType");
                }
				val = (pure.utils.tween.EaseType)e;
                
            }
            else{
                val = (pure.utils.tween.EaseType)objectCasters.GetCaster(typeof(pure.utils.tween.EaseType))(L, index, null);
            }
        }

        public void UpdatepureutilstweenEaseType(RealStatePtr L, int index, pure.utils.tween.EaseType val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenEaseType_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.EaseType");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.EaseType ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenTweenParamater_TypeID = -1;
		int pureutilstweenTweenParamater_EnumRef = -1;
        
        public void PushpureutilstweenTweenParamater(RealStatePtr L, pure.utils.tween.TweenParamater val){
        	if (pureutilstweenTweenParamater_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenTweenParamater_TypeID = getTypeId(L, typeof(pure.utils.tween.TweenParamater), out is_first);
				
				if (pureutilstweenTweenParamater_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.TweenParamater));
				    pureutilstweenTweenParamater_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenTweenParamater_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenTweenParamater_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.TweenParamater ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenTweenParamater_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.TweenParamater val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenParamater_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenParamater");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.TweenParamater");
                }
				val = (pure.utils.tween.TweenParamater)e;
                
            }
            else{
                val = (pure.utils.tween.TweenParamater)objectCasters.GetCaster(typeof(pure.utils.tween.TweenParamater))(L, index, null);
            }
        }

        public void UpdatepureutilstweenTweenParamater(RealStatePtr L, int index, pure.utils.tween.TweenParamater val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenParamater_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenParamater");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.TweenParamater ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenTweenGroupMode_TypeID = -1;
		int pureutilstweenTweenGroupMode_EnumRef = -1;
        
        public void PushpureutilstweenTweenGroupMode(RealStatePtr L, pure.utils.tween.TweenGroupMode val){
        	if (pureutilstweenTweenGroupMode_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenTweenGroupMode_TypeID = getTypeId(L, typeof(pure.utils.tween.TweenGroupMode), out is_first);
				
				if (pureutilstweenTweenGroupMode_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.TweenGroupMode));
				    pureutilstweenTweenGroupMode_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenTweenGroupMode_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenTweenGroupMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.TweenGroupMode ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenTweenGroupMode_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.TweenGroupMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenGroupMode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenGroupMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.TweenGroupMode");
                }
				val = (pure.utils.tween.TweenGroupMode)e;
                
            }
            else{
                val = (pure.utils.tween.TweenGroupMode)objectCasters.GetCaster(typeof(pure.utils.tween.TweenGroupMode))(L, index, null);
            }
        }

        public void UpdatepureutilstweenTweenGroupMode(RealStatePtr L, int index, pure.utils.tween.TweenGroupMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenGroupMode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenGroupMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.TweenGroupMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenTweenState_TypeID = -1;
		int pureutilstweenTweenState_EnumRef = -1;
        
        public void PushpureutilstweenTweenState(RealStatePtr L, pure.utils.tween.TweenState val){
        	if (pureutilstweenTweenState_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenTweenState_TypeID = getTypeId(L, typeof(pure.utils.tween.TweenState), out is_first);
				
				if (pureutilstweenTweenState_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.TweenState));
				    pureutilstweenTweenState_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenTweenState_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenTweenState_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.TweenState ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenTweenState_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.TweenState val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenState_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenState");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.TweenState");
                }
				val = (pure.utils.tween.TweenState)e;
                
            }
            else{
                val = (pure.utils.tween.TweenState)objectCasters.GetCaster(typeof(pure.utils.tween.TweenState))(L, index, null);
            }
        }

        public void UpdatepureutilstweenTweenState(RealStatePtr L, int index, pure.utils.tween.TweenState val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenState_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenState");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.TweenState ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilsmathToolsHashCode_TypeID = -1;
        public void PushpureutilsmathToolsHashCode(RealStatePtr L, pure.utils.mathTools.HashCode val){
        	if (pureutilsmathToolsHashCode_TypeID == -1)
            {
			    bool is_first;
                pureutilsmathToolsHashCode_TypeID = getTypeId(L, typeof(pure.utils.mathTools.HashCode), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, pureutilsmathToolsHashCode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for pure.utils.mathTools.HashCode ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.mathTools.HashCode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilsmathToolsHashCode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.mathTools.HashCode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for pure.utils.mathTools.HashCode");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (pure.utils.mathTools.HashCode)objectCasters.GetCaster(typeof(pure.utils.mathTools.HashCode))(L, index, null);
            }
        }

        public void UpdatepureutilsmathToolsHashCode(RealStatePtr L, int index, pure.utils.mathTools.HashCode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilsmathToolsHashCode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.mathTools.HashCode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for pure.utils.mathTools.HashCode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilsinputButtonStatus_TypeID = -1;
		int pureutilsinputButtonStatus_EnumRef = -1;
        
        public void PushpureutilsinputButtonStatus(RealStatePtr L, pure.utils.input.ButtonStatus val){
        	if (pureutilsinputButtonStatus_TypeID == -1)
            {
			    bool is_first;
                pureutilsinputButtonStatus_TypeID = getTypeId(L, typeof(pure.utils.input.ButtonStatus), out is_first);
				
				if (pureutilsinputButtonStatus_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.input.ButtonStatus));
				    pureutilsinputButtonStatus_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilsinputButtonStatus_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilsinputButtonStatus_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.input.ButtonStatus ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilsinputButtonStatus_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.input.ButtonStatus val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilsinputButtonStatus_TypeID){
				    throw new Exception("invalid userdata for pure.utils.input.ButtonStatus");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.input.ButtonStatus");
                }
				val = (pure.utils.input.ButtonStatus)e;
                
            }
            else{
                val = (pure.utils.input.ButtonStatus)objectCasters.GetCaster(typeof(pure.utils.input.ButtonStatus))(L, index, null);
            }
        }

        public void UpdatepureutilsinputButtonStatus(RealStatePtr L, int index, pure.utils.input.ButtonStatus val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilsinputButtonStatus_TypeID){
				    throw new Exception("invalid userdata for pure.utils.input.ButtonStatus");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.input.ButtonStatus ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureuiMovieEvent_TypeID = -1;
		int pureuiMovieEvent_EnumRef = -1;
        
        public void PushpureuiMovieEvent(RealStatePtr L, pure.ui.MovieEvent val){
        	if (pureuiMovieEvent_TypeID == -1)
            {
			    bool is_first;
                pureuiMovieEvent_TypeID = getTypeId(L, typeof(pure.ui.MovieEvent), out is_first);
				
				if (pureuiMovieEvent_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.ui.MovieEvent));
				    pureuiMovieEvent_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureuiMovieEvent_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureuiMovieEvent_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.ui.MovieEvent ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureuiMovieEvent_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.ui.MovieEvent val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiMovieEvent_TypeID){
				    throw new Exception("invalid userdata for pure.ui.MovieEvent");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.ui.MovieEvent");
                }
				val = (pure.ui.MovieEvent)e;
                
            }
            else{
                val = (pure.ui.MovieEvent)objectCasters.GetCaster(typeof(pure.ui.MovieEvent))(L, index, null);
            }
        }

        public void UpdatepureuiMovieEvent(RealStatePtr L, int index, pure.ui.MovieEvent val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiMovieEvent_TypeID){
				    throw new Exception("invalid userdata for pure.ui.MovieEvent");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.ui.MovieEvent ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureuiUITweenParameter_TypeID = -1;
		int pureuiUITweenParameter_EnumRef = -1;
        
        public void PushpureuiUITweenParameter(RealStatePtr L, pure.ui.UITweenParameter val){
        	if (pureuiUITweenParameter_TypeID == -1)
            {
			    bool is_first;
                pureuiUITweenParameter_TypeID = getTypeId(L, typeof(pure.ui.UITweenParameter), out is_first);
				
				if (pureuiUITweenParameter_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.ui.UITweenParameter));
				    pureuiUITweenParameter_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureuiUITweenParameter_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureuiUITweenParameter_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.ui.UITweenParameter ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureuiUITweenParameter_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.ui.UITweenParameter val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiUITweenParameter_TypeID){
				    throw new Exception("invalid userdata for pure.ui.UITweenParameter");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.ui.UITweenParameter");
                }
				val = (pure.ui.UITweenParameter)e;
                
            }
            else{
                val = (pure.ui.UITweenParameter)objectCasters.GetCaster(typeof(pure.ui.UITweenParameter))(L, index, null);
            }
        }

        public void UpdatepureuiUITweenParameter(RealStatePtr L, int index, pure.ui.UITweenParameter val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiUITweenParameter_TypeID){
				    throw new Exception("invalid userdata for pure.ui.UITweenParameter");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.ui.UITweenParameter ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureuiPRepeatImage_DllDirection_TypeID = -1;
		int pureuiPRepeatImage_DllDirection_EnumRef = -1;
        
        public void PushpureuiPRepeatImage_DllDirection(RealStatePtr L, pure.ui.PRepeatImage_Dll.Direction val){
        	if (pureuiPRepeatImage_DllDirection_TypeID == -1)
            {
			    bool is_first;
                pureuiPRepeatImage_DllDirection_TypeID = getTypeId(L, typeof(pure.ui.PRepeatImage_Dll.Direction), out is_first);
				
				if (pureuiPRepeatImage_DllDirection_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.ui.PRepeatImage_Dll.Direction));
				    pureuiPRepeatImage_DllDirection_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureuiPRepeatImage_DllDirection_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureuiPRepeatImage_DllDirection_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.ui.PRepeatImage_Dll.Direction ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureuiPRepeatImage_DllDirection_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.ui.PRepeatImage_Dll.Direction val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiPRepeatImage_DllDirection_TypeID){
				    throw new Exception("invalid userdata for pure.ui.PRepeatImage_Dll.Direction");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.ui.PRepeatImage_Dll.Direction");
                }
				val = (pure.ui.PRepeatImage_Dll.Direction)e;
                
            }
            else{
                val = (pure.ui.PRepeatImage_Dll.Direction)objectCasters.GetCaster(typeof(pure.ui.PRepeatImage_Dll.Direction))(L, index, null);
            }
        }

        public void UpdatepureuiPRepeatImage_DllDirection(RealStatePtr L, int index, pure.ui.PRepeatImage_Dll.Direction val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiPRepeatImage_DllDirection_TypeID){
				    throw new Exception("invalid userdata for pure.ui.PRepeatImage_Dll.Direction");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.ui.PRepeatImage_Dll.Direction ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineVector2_TypeID = -1;
        public void PushUnityEngineVector2(RealStatePtr L, UnityEngine.Vector2 val){
        	if (UnityEngineVector2_TypeID == -1)
            {
			    bool is_first;
                UnityEngineVector2_TypeID = getTypeId(L, typeof(UnityEngine.Vector2), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 8, UnityEngineVector2_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Vector2 ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Vector2 val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector2_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector2");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Vector2");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Vector2)objectCasters.GetCaster(typeof(UnityEngine.Vector2))(L, index, null);
            }
        }

        public void UpdateUnityEngineVector2(RealStatePtr L, int index, UnityEngine.Vector2 val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector2_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector2");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Vector2 ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineVector3_TypeID = -1;
        public void PushUnityEngineVector3(RealStatePtr L, UnityEngine.Vector3 val){
        	if (UnityEngineVector3_TypeID == -1)
            {
			    bool is_first;
                UnityEngineVector3_TypeID = getTypeId(L, typeof(UnityEngine.Vector3), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 12, UnityEngineVector3_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Vector3 ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Vector3 val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector3_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector3");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Vector3");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Vector3)objectCasters.GetCaster(typeof(UnityEngine.Vector3))(L, index, null);
            }
        }

        public void UpdateUnityEngineVector3(RealStatePtr L, int index, UnityEngine.Vector3 val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector3_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector3");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Vector3 ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineVector4_TypeID = -1;
        public void PushUnityEngineVector4(RealStatePtr L, UnityEngine.Vector4 val){
        	if (UnityEngineVector4_TypeID == -1)
            {
			    bool is_first;
                UnityEngineVector4_TypeID = getTypeId(L, typeof(UnityEngine.Vector4), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, UnityEngineVector4_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Vector4 ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Vector4 val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector4_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector4");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Vector4");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Vector4)objectCasters.GetCaster(typeof(UnityEngine.Vector4))(L, index, null);
            }
        }

        public void UpdateUnityEngineVector4(RealStatePtr L, int index, UnityEngine.Vector4 val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineVector4_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Vector4");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Vector4 ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineColor_TypeID = -1;
        public void PushUnityEngineColor(RealStatePtr L, UnityEngine.Color val){
        	if (UnityEngineColor_TypeID == -1)
            {
			    bool is_first;
                UnityEngineColor_TypeID = getTypeId(L, typeof(UnityEngine.Color), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, UnityEngineColor_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Color ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Color val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineColor_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Color");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Color");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Color)objectCasters.GetCaster(typeof(UnityEngine.Color))(L, index, null);
            }
        }

        public void UpdateUnityEngineColor(RealStatePtr L, int index, UnityEngine.Color val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineColor_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Color");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Color ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineQuaternion_TypeID = -1;
        public void PushUnityEngineQuaternion(RealStatePtr L, UnityEngine.Quaternion val){
        	if (UnityEngineQuaternion_TypeID == -1)
            {
			    bool is_first;
                UnityEngineQuaternion_TypeID = getTypeId(L, typeof(UnityEngine.Quaternion), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, UnityEngineQuaternion_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Quaternion ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Quaternion val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineQuaternion_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Quaternion");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Quaternion");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Quaternion)objectCasters.GetCaster(typeof(UnityEngine.Quaternion))(L, index, null);
            }
        }

        public void UpdateUnityEngineQuaternion(RealStatePtr L, int index, UnityEngine.Quaternion val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineQuaternion_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Quaternion");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Quaternion ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineRay_TypeID = -1;
        public void PushUnityEngineRay(RealStatePtr L, UnityEngine.Ray val){
        	if (UnityEngineRay_TypeID == -1)
            {
			    bool is_first;
                UnityEngineRay_TypeID = getTypeId(L, typeof(UnityEngine.Ray), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 24, UnityEngineRay_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Ray ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Ray val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineRay_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Ray");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Ray");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Ray)objectCasters.GetCaster(typeof(UnityEngine.Ray))(L, index, null);
            }
        }

        public void UpdateUnityEngineRay(RealStatePtr L, int index, UnityEngine.Ray val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineRay_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Ray");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Ray ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineBounds_TypeID = -1;
        public void PushUnityEngineBounds(RealStatePtr L, UnityEngine.Bounds val){
        	if (UnityEngineBounds_TypeID == -1)
            {
			    bool is_first;
                UnityEngineBounds_TypeID = getTypeId(L, typeof(UnityEngine.Bounds), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 24, UnityEngineBounds_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Bounds ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Bounds val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineBounds_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Bounds");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Bounds");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Bounds)objectCasters.GetCaster(typeof(UnityEngine.Bounds))(L, index, null);
            }
        }

        public void UpdateUnityEngineBounds(RealStatePtr L, int index, UnityEngine.Bounds val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineBounds_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Bounds");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Bounds ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int UnityEngineRay2D_TypeID = -1;
        public void PushUnityEngineRay2D(RealStatePtr L, UnityEngine.Ray2D val){
        	if (UnityEngineRay2D_TypeID == -1)
            {
			    bool is_first;
                UnityEngineRay2D_TypeID = getTypeId(L, typeof(UnityEngine.Ray2D), out is_first);
				
            }
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 16, UnityEngineRay2D_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, val)){
                throw new Exception("pack fail fail for UnityEngine.Ray2D ,value="+val);
            }
			
        }

        public void Get(RealStatePtr L, int index, out UnityEngine.Ray2D val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineRay2D_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Ray2D");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);if (!CopyByValue.instance.UnPack(buff, 0, out val)) {
                    throw new Exception("unpack fail for UnityEngine.Ray2D");
                }
            }
			else if (type ==LuaTypes.LUA_TTABLE){
			    CopyByValue.instance.UnPack(this, L, index, out val);
			}
            else{
                val = (UnityEngine.Ray2D)objectCasters.GetCaster(typeof(UnityEngine.Ray2D))(L, index, null);
            }
        }

        public void UpdateUnityEngineRay2D(RealStatePtr L, int index, UnityEngine.Ray2D val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != UnityEngineRay2D_TypeID){
				    throw new Exception("invalid userdata for UnityEngine.Ray2D");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  val)){
                    throw new Exception("pack fail for UnityEngine.Ray2D ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenEasePathType_TypeID = -1;
		int pureutilstweenEasePathType_EnumRef = -1;
        
        public void PushpureutilstweenEasePathType(RealStatePtr L, pure.utils.tween.EasePathType val){
        	if (pureutilstweenEasePathType_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenEasePathType_TypeID = getTypeId(L, typeof(pure.utils.tween.EasePathType), out is_first);
				
				if (pureutilstweenEasePathType_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.EasePathType));
				    pureutilstweenEasePathType_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenEasePathType_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenEasePathType_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.EasePathType ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenEasePathType_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.EasePathType val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenEasePathType_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.EasePathType");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.EasePathType");
                }
				val = (pure.utils.tween.EasePathType)e;
                
            }
            else{
                val = (pure.utils.tween.EasePathType)objectCasters.GetCaster(typeof(pure.utils.tween.EasePathType))(L, index, null);
            }
        }

        public void UpdatepureutilstweenEasePathType(RealStatePtr L, int index, pure.utils.tween.EasePathType val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenEasePathType_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.EasePathType");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.EasePathType ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenTweenTargetMode_TypeID = -1;
		int pureutilstweenTweenTargetMode_EnumRef = -1;
        
        public void PushpureutilstweenTweenTargetMode(RealStatePtr L, pure.utils.tween.TweenTargetMode val){
        	if (pureutilstweenTweenTargetMode_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenTweenTargetMode_TypeID = getTypeId(L, typeof(pure.utils.tween.TweenTargetMode), out is_first);
				
				if (pureutilstweenTweenTargetMode_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.TweenTargetMode));
				    pureutilstweenTweenTargetMode_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenTweenTargetMode_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenTweenTargetMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.TweenTargetMode ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenTweenTargetMode_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.TweenTargetMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenTargetMode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenTargetMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.TweenTargetMode");
                }
				val = (pure.utils.tween.TweenTargetMode)e;
                
            }
            else{
                val = (pure.utils.tween.TweenTargetMode)objectCasters.GetCaster(typeof(pure.utils.tween.TweenTargetMode))(L, index, null);
            }
        }

        public void UpdatepureutilstweenTweenTargetMode(RealStatePtr L, int index, pure.utils.tween.TweenTargetMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenTargetMode_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenTargetMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.TweenTargetMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureutilstweenTweenDirection_TypeID = -1;
		int pureutilstweenTweenDirection_EnumRef = -1;
        
        public void PushpureutilstweenTweenDirection(RealStatePtr L, pure.utils.tween.TweenDirection val){
        	if (pureutilstweenTweenDirection_TypeID == -1)
            {
			    bool is_first;
                pureutilstweenTweenDirection_TypeID = getTypeId(L, typeof(pure.utils.tween.TweenDirection), out is_first);
				
				if (pureutilstweenTweenDirection_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.utils.tween.TweenDirection));
				    pureutilstweenTweenDirection_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureutilstweenTweenDirection_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureutilstweenTweenDirection_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.utils.tween.TweenDirection ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureutilstweenTweenDirection_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.utils.tween.TweenDirection val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenDirection_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenDirection");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.utils.tween.TweenDirection");
                }
				val = (pure.utils.tween.TweenDirection)e;
                
            }
            else{
                val = (pure.utils.tween.TweenDirection)objectCasters.GetCaster(typeof(pure.utils.tween.TweenDirection))(L, index, null);
            }
        }

        public void UpdatepureutilstweenTweenDirection(RealStatePtr L, int index, pure.utils.tween.TweenDirection val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureutilstweenTweenDirection_TypeID){
				    throw new Exception("invalid userdata for pure.utils.tween.TweenDirection");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.utils.tween.TweenDirection ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        int pureuiNumberField_DllNumberChangeMode_TypeID = -1;
		int pureuiNumberField_DllNumberChangeMode_EnumRef = -1;
        
        public void PushpureuiNumberField_DllNumberChangeMode(RealStatePtr L, pure.ui.NumberField_Dll.NumberChangeMode val){
        	if (pureuiNumberField_DllNumberChangeMode_TypeID == -1)
            {
			    bool is_first;
                pureuiNumberField_DllNumberChangeMode_TypeID = getTypeId(L, typeof(pure.ui.NumberField_Dll.NumberChangeMode), out is_first);
				
				if (pureuiNumberField_DllNumberChangeMode_EnumRef == -1)
				{
				    Utils.LoadCSTable(L, typeof(pure.ui.NumberField_Dll.NumberChangeMode));
				    pureuiNumberField_DllNumberChangeMode_EnumRef = LuaAPI.luaL_ref(L, LuaIndexes.LUA_REGISTRYINDEX);
				}
				
            }
			
			if (LuaAPI.xlua_tryget_cachedud(L, (int)val, pureuiNumberField_DllNumberChangeMode_EnumRef) == 1){
			    return;
			}
			
            IntPtr buff = LuaAPI.xlua_pushstruct(L, 4, pureuiNumberField_DllNumberChangeMode_TypeID);
            if (!CopyByValue.instance.Pack(buff, 0, (int)val)){
                throw new Exception("pack fail fail for pure.ui.NumberField_Dll.NumberChangeMode ,value="+val);
            }
			
			LuaAPI.lua_getref(L, pureuiNumberField_DllNumberChangeMode_EnumRef);
			LuaAPI.lua_pushvalue(L, -2);
			LuaAPI.xlua_rawseti(L, -2, (int)val);
			LuaAPI.lua_pop(L, 1);
			
        }

        public void Get(RealStatePtr L, int index, out pure.ui.NumberField_Dll.NumberChangeMode val){
	  		LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ) {
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiNumberField_DllNumberChangeMode_TypeID){
				    throw new Exception("invalid userdata for pure.ui.NumberField_Dll.NumberChangeMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
				int e;
                if (!CopyByValue.instance.UnPack(buff, 0, out e)) {
                    throw new Exception("unpack fail for pure.ui.NumberField_Dll.NumberChangeMode");
                }
				val = (pure.ui.NumberField_Dll.NumberChangeMode)e;
                
            }
            else{
                val = (pure.ui.NumberField_Dll.NumberChangeMode)objectCasters.GetCaster(typeof(pure.ui.NumberField_Dll.NumberChangeMode))(L, index, null);
            }
        }

        public void UpdatepureuiNumberField_DllNumberChangeMode(RealStatePtr L, int index, pure.ui.NumberField_Dll.NumberChangeMode val){
	 		
            if (LuaAPI.lua_type(L, index) == LuaTypes.LUA_TUSERDATA){
			    if (LuaAPI.xlua_gettypeid(L, index) != pureuiNumberField_DllNumberChangeMode_TypeID){
				    throw new Exception("invalid userdata for pure.ui.NumberField_Dll.NumberChangeMode");
				}

                IntPtr buff = LuaAPI.lua_touserdata(L, index);
                if (!CopyByValue.instance.Pack(buff, 0,  (int)val)){
                    throw new Exception("pack fail for pure.ui.NumberField_Dll.NumberChangeMode ,value="+val);
                }
            }
			
            else
            {
                throw new Exception("try to update a data with lua type:" + LuaAPI.lua_type(L, index));
            }
        }

        
		// table cast optimze
		
		public void Get(RealStatePtr L, int index, out game.mono.range.SightData val){
		    LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ){
			    val = (game.mono.range.SightData)FastGetCSObj(L, index);
            }
			else if (type == LuaTypes.LUA_TTABLE){
			    val = new game.mono.range.SightData();
				int top = LuaAPI.lua_gettop(L);
				
				if (Utils.LoadField(L, index, "type")){
					Get(L, top + 1, out val.type);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "radius")){
					Get(L, top + 1, out val.radius);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "angle")){
					Get(L, top + 1, out val.angle);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "thickness")){
					Get(L, top + 1, out val.thickness);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "offset")){
					Get(L, top + 1, out val.offset);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "rotate")){
					Get(L, top + 1, out val.rotate);
				}
				LuaAPI.lua_pop(L, 1);
				
			}
            else{
                throw new Exception("can not cast " + LuaAPI.lua_type(L, index) + " to " + typeof(game.mono.range.SightData));
            }
        }
		
		public void Get(RealStatePtr L, int index, out game.ai.machine.vo.DamageCalc val){
		    LuaTypes type = LuaAPI.lua_type(L, index);
            if (type == LuaTypes.LUA_TUSERDATA ){
			    val = (game.ai.machine.vo.DamageCalc)FastGetCSObj(L, index);
            }
			else if (type == LuaTypes.LUA_TTABLE){
			    val = new game.ai.machine.vo.DamageCalc();
				int top = LuaAPI.lua_gettop(L);
				
				if (Utils.LoadField(L, index, "moveIndex")){
					Get(L, top + 1, out val.moveIndex);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "scope")){
					Get(L, top + 1, out val.scope);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "elementMode")){
					Get(L, top + 1, out val.elementMode);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "rangeMode")){
					Get(L, top + 1, out val.rangeMode);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "ceoffMode")){
					Get(L, top + 1, out val.ceoffMode);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "weaponMode")){
					Get(L, top + 1, out val.weaponMode);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "attackAngle")){
					Get(L, top + 1, out val.attackAngle);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "hitQty")){
					Get(L, top + 1, out val.hitQty);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "totalHitQty")){
					Get(L, top + 1, out val.totalHitQty);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "fxIndex")){
					Get(L, top + 1, out val.fxIndex);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "shield")){
					Get(L, top + 1, out val.shield);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "pushDistance")){
					Get(L, top + 1, out val.pushDistance);
				}
				LuaAPI.lua_pop(L, 1);
				
				if (Utils.LoadField(L, index, "targetOnAir")){
					Get(L, top + 1, out val.targetOnAir);
				}
				LuaAPI.lua_pop(L, 1);
				
			}
            else{
                throw new Exception("can not cast " + LuaAPI.lua_type(L, index) + " to " + typeof(game.ai.machine.vo.DamageCalc));
            }
        }
		

    }

	public static class StaticLuaCallbacksWrap {
	    internal static bool __tryArrayGet(Type type, RealStatePtr L, ObjectTranslator translator, object obj, int index){
	    	WrapPusher p= translator as WrapPusher;
	        if (p == null) return false;
		
			if (type == typeof(game.ui.ScrollVO[])){
			    game.ui.ScrollVO[] array = obj as game.ui.ScrollVO[];
				p.PushgameuiScrollVO(L, array[index]);
				return true;
			}
			else if (type == typeof(game.mono.manager.SceneSpecialMode[])){
			    game.mono.manager.SceneSpecialMode[] array = obj as game.mono.manager.SceneSpecialMode[];
				p.PushgamemonomanagerSceneSpecialMode(L, array[index]);
				return true;
			}
			else if (type == typeof(game.mono.avatar.AvatarCondition[])){
			    game.mono.avatar.AvatarCondition[] array = obj as game.mono.avatar.AvatarCondition[];
				p.PushgamemonoavatarAvatarCondition(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.entity.OperationCode[])){
			    game.ai.entity.OperationCode[] array = obj as game.ai.entity.OperationCode[];
				p.PushgameaientityOperationCode(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.entity.OperationVO[])){
			    game.ai.entity.OperationVO[] array = obj as game.ai.entity.OperationVO[];
				p.PushgameaientityOperationVO(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.entity.avatar.RenderSlot[])){
			    game.ai.entity.avatar.RenderSlot[] array = obj as game.ai.entity.avatar.RenderSlot[];
				p.PushgameaientityavatarRenderSlot(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.personality.NpcPropertyUnit[])){
			    game.ai.personality.NpcPropertyUnit[] array = obj as game.ai.personality.NpcPropertyUnit[];
				p.PushgameaipersonalityNpcPropertyUnit(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.AnimVO[])){
			    game.ai.machine.vo.AnimVO[] array = obj as game.ai.machine.vo.AnimVO[];
				p.PushgameaimachinevoAnimVO(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.SkillCeoffModifier[])){
			    game.ai.machine.vo.SkillCeoffModifier[] array = obj as game.ai.machine.vo.SkillCeoffModifier[];
				p.PushgameaimachinevoSkillCeoffModifier(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.SkillRetrieverInfo[])){
			    game.ai.machine.vo.SkillRetrieverInfo[] array = obj as game.ai.machine.vo.SkillRetrieverInfo[];
				p.PushgameaimachinevoSkillRetrieverInfo(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.FxBuffVO[])){
			    game.ai.machine.vo.FxBuffVO[] array = obj as game.ai.machine.vo.FxBuffVO[];
				p.PushgameaimachinevoFxBuffVO(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.ComboVO[])){
			    game.ai.machine.vo.ComboVO[] array = obj as game.ai.machine.vo.ComboVO[];
				p.PushgameaimachinevoComboVO(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.FightResult[])){
			    game.ai.machine.vo.FightResult[] array = obj as game.ai.machine.vo.FightResult[];
				p.PushgameaimachinevoFightResult(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_AIMode[])){
			    game.ai.client.Action_AIMode[] array = obj as game.ai.client.Action_AIMode[];
				p.PushgameaiclientAction_AIMode(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_Animation[])){
			    game.ai.client.Action_Animation[] array = obj as game.ai.client.Action_Animation[];
				p.PushgameaiclientAction_Animation(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_AutoMode[])){
			    game.ai.client.Action_AutoMode[] array = obj as game.ai.client.Action_AutoMode[];
				p.PushgameaiclientAction_AutoMode(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BuffControl[])){
			    game.ai.client.Action_BuffControl[] array = obj as game.ai.client.Action_BuffControl[];
				p.PushgameaiclientAction_BuffControl(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BuffShow[])){
			    game.ai.client.Action_BuffShow[] array = obj as game.ai.client.Action_BuffShow[];
				p.PushgameaiclientAction_BuffShow(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletAdd[])){
			    game.ai.client.Action_BulletAdd[] array = obj as game.ai.client.Action_BulletAdd[];
				p.PushgameaiclientAction_BulletAdd(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletTransform[])){
			    game.ai.client.Action_BulletTransform[] array = obj as game.ai.client.Action_BulletTransform[];
				p.PushgameaiclientAction_BulletTransform(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletBind[])){
			    game.ai.client.Action_BulletBind[] array = obj as game.ai.client.Action_BulletBind[];
				p.PushgameaiclientAction_BulletBind(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_IOLock[])){
			    game.ai.client.Action_IOLock[] array = obj as game.ai.client.Action_IOLock[];
				p.PushgameaiclientAction_IOLock(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_RemoveEntity[])){
			    game.ai.client.Action_RemoveEntity[] array = obj as game.ai.client.Action_RemoveEntity[];
				p.PushgameaiclientAction_RemoveEntity(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_ShaderSlot[])){
			    game.ai.client.Action_ShaderSlot[] array = obj as game.ai.client.Action_ShaderSlot[];
				p.PushgameaiclientAction_ShaderSlot(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SkillLocate[])){
			    game.ai.client.Action_SkillLocate[] array = obj as game.ai.client.Action_SkillLocate[];
				p.PushgameaiclientAction_SkillLocate(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SpeakBubble[])){
			    game.ai.client.Action_SpeakBubble[] array = obj as game.ai.client.Action_SpeakBubble[];
				p.PushgameaiclientAction_SpeakBubble(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SpeedMulti[])){
			    game.ai.client.Action_SpeedMulti[] array = obj as game.ai.client.Action_SpeedMulti[];
				p.PushgameaiclientAction_SpeedMulti(L, array[index]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_Transform[])){
			    game.ai.client.Action_Transform[] array = obj as game.ai.client.Action_Transform[];
				p.PushgameaiclientAction_Transform(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.EaseType[])){
			    pure.utils.tween.EaseType[] array = obj as pure.utils.tween.EaseType[];
				p.PushpureutilstweenEaseType(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenParamater[])){
			    pure.utils.tween.TweenParamater[] array = obj as pure.utils.tween.TweenParamater[];
				p.PushpureutilstweenTweenParamater(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenGroupMode[])){
			    pure.utils.tween.TweenGroupMode[] array = obj as pure.utils.tween.TweenGroupMode[];
				p.PushpureutilstweenTweenGroupMode(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenState[])){
			    pure.utils.tween.TweenState[] array = obj as pure.utils.tween.TweenState[];
				p.PushpureutilstweenTweenState(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.mathTools.HashCode[])){
			    pure.utils.mathTools.HashCode[] array = obj as pure.utils.mathTools.HashCode[];
				p.PushpureutilsmathToolsHashCode(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.input.ButtonStatus[])){
			    pure.utils.input.ButtonStatus[] array = obj as pure.utils.input.ButtonStatus[];
				p.PushpureutilsinputButtonStatus(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.ui.MovieEvent[])){
			    pure.ui.MovieEvent[] array = obj as pure.ui.MovieEvent[];
				p.PushpureuiMovieEvent(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.ui.UITweenParameter[])){
			    pure.ui.UITweenParameter[] array = obj as pure.ui.UITweenParameter[];
				p.PushpureuiUITweenParameter(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.ui.PRepeatImage_Dll.Direction[])){
			    pure.ui.PRepeatImage_Dll.Direction[] array = obj as pure.ui.PRepeatImage_Dll.Direction[];
				p.PushpureuiPRepeatImage_DllDirection(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector2[])){
			    UnityEngine.Vector2[] array = obj as UnityEngine.Vector2[];
				p.PushUnityEngineVector2(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector3[])){
			    UnityEngine.Vector3[] array = obj as UnityEngine.Vector3[];
				p.PushUnityEngineVector3(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector4[])){
			    UnityEngine.Vector4[] array = obj as UnityEngine.Vector4[];
				p.PushUnityEngineVector4(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Color[])){
			    UnityEngine.Color[] array = obj as UnityEngine.Color[];
				p.PushUnityEngineColor(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Quaternion[])){
			    UnityEngine.Quaternion[] array = obj as UnityEngine.Quaternion[];
				p.PushUnityEngineQuaternion(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Ray[])){
			    UnityEngine.Ray[] array = obj as UnityEngine.Ray[];
				p.PushUnityEngineRay(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Bounds[])){
			    UnityEngine.Bounds[] array = obj as UnityEngine.Bounds[];
				p.PushUnityEngineBounds(L, array[index]);
				return true;
			}
			else if (type == typeof(UnityEngine.Ray2D[])){
			    UnityEngine.Ray2D[] array = obj as UnityEngine.Ray2D[];
				p.PushUnityEngineRay2D(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.EasePathType[])){
			    pure.utils.tween.EasePathType[] array = obj as pure.utils.tween.EasePathType[];
				p.PushpureutilstweenEasePathType(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenTargetMode[])){
			    pure.utils.tween.TweenTargetMode[] array = obj as pure.utils.tween.TweenTargetMode[];
				p.PushpureutilstweenTweenTargetMode(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenDirection[])){
			    pure.utils.tween.TweenDirection[] array = obj as pure.utils.tween.TweenDirection[];
				p.PushpureutilstweenTweenDirection(L, array[index]);
				return true;
			}
			else if (type == typeof(pure.ui.NumberField_Dll.NumberChangeMode[])){
			    pure.ui.NumberField_Dll.NumberChangeMode[] array = obj as pure.ui.NumberField_Dll.NumberChangeMode[];
				p.PushpureuiNumberField_DllNumberChangeMode(L, array[index]);
				return true;
			}
            return false;
		}

		internal static bool __tryArraySet(Type type, RealStatePtr L, ObjectTranslator translator, object obj, int array_idx, int obj_idx){
			WrapPusher p= translator as WrapPusher;
	        if (p == null) return false;
		
			if (type == typeof(game.ui.ScrollVO[])){
			    game.ui.ScrollVO[] array = obj as game.ui.ScrollVO[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.mono.manager.SceneSpecialMode[])){
			    game.mono.manager.SceneSpecialMode[] array = obj as game.mono.manager.SceneSpecialMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.mono.avatar.AvatarCondition[])){
			    game.mono.avatar.AvatarCondition[] array = obj as game.mono.avatar.AvatarCondition[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.entity.OperationCode[])){
			    game.ai.entity.OperationCode[] array = obj as game.ai.entity.OperationCode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.entity.OperationVO[])){
			    game.ai.entity.OperationVO[] array = obj as game.ai.entity.OperationVO[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.entity.avatar.RenderSlot[])){
			    game.ai.entity.avatar.RenderSlot[] array = obj as game.ai.entity.avatar.RenderSlot[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.personality.NpcPropertyUnit[])){
			    game.ai.personality.NpcPropertyUnit[] array = obj as game.ai.personality.NpcPropertyUnit[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.AnimVO[])){
			    game.ai.machine.vo.AnimVO[] array = obj as game.ai.machine.vo.AnimVO[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.SkillCeoffModifier[])){
			    game.ai.machine.vo.SkillCeoffModifier[] array = obj as game.ai.machine.vo.SkillCeoffModifier[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.SkillRetrieverInfo[])){
			    game.ai.machine.vo.SkillRetrieverInfo[] array = obj as game.ai.machine.vo.SkillRetrieverInfo[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.FxBuffVO[])){
			    game.ai.machine.vo.FxBuffVO[] array = obj as game.ai.machine.vo.FxBuffVO[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.ComboVO[])){
			    game.ai.machine.vo.ComboVO[] array = obj as game.ai.machine.vo.ComboVO[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.FightResult[])){
			    game.ai.machine.vo.FightResult[] array = obj as game.ai.machine.vo.FightResult[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_AIMode[])){
			    game.ai.client.Action_AIMode[] array = obj as game.ai.client.Action_AIMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_Animation[])){
			    game.ai.client.Action_Animation[] array = obj as game.ai.client.Action_Animation[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_AutoMode[])){
			    game.ai.client.Action_AutoMode[] array = obj as game.ai.client.Action_AutoMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BuffControl[])){
			    game.ai.client.Action_BuffControl[] array = obj as game.ai.client.Action_BuffControl[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BuffShow[])){
			    game.ai.client.Action_BuffShow[] array = obj as game.ai.client.Action_BuffShow[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletAdd[])){
			    game.ai.client.Action_BulletAdd[] array = obj as game.ai.client.Action_BulletAdd[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletTransform[])){
			    game.ai.client.Action_BulletTransform[] array = obj as game.ai.client.Action_BulletTransform[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_BulletBind[])){
			    game.ai.client.Action_BulletBind[] array = obj as game.ai.client.Action_BulletBind[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_IOLock[])){
			    game.ai.client.Action_IOLock[] array = obj as game.ai.client.Action_IOLock[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_RemoveEntity[])){
			    game.ai.client.Action_RemoveEntity[] array = obj as game.ai.client.Action_RemoveEntity[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_ShaderSlot[])){
			    game.ai.client.Action_ShaderSlot[] array = obj as game.ai.client.Action_ShaderSlot[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SkillLocate[])){
			    game.ai.client.Action_SkillLocate[] array = obj as game.ai.client.Action_SkillLocate[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SpeakBubble[])){
			    game.ai.client.Action_SpeakBubble[] array = obj as game.ai.client.Action_SpeakBubble[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_SpeedMulti[])){
			    game.ai.client.Action_SpeedMulti[] array = obj as game.ai.client.Action_SpeedMulti[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.client.Action_Transform[])){
			    game.ai.client.Action_Transform[] array = obj as game.ai.client.Action_Transform[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.EaseType[])){
			    pure.utils.tween.EaseType[] array = obj as pure.utils.tween.EaseType[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenParamater[])){
			    pure.utils.tween.TweenParamater[] array = obj as pure.utils.tween.TweenParamater[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenGroupMode[])){
			    pure.utils.tween.TweenGroupMode[] array = obj as pure.utils.tween.TweenGroupMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenState[])){
			    pure.utils.tween.TweenState[] array = obj as pure.utils.tween.TweenState[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.mathTools.HashCode[])){
			    pure.utils.mathTools.HashCode[] array = obj as pure.utils.mathTools.HashCode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.input.ButtonStatus[])){
			    pure.utils.input.ButtonStatus[] array = obj as pure.utils.input.ButtonStatus[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.ui.MovieEvent[])){
			    pure.ui.MovieEvent[] array = obj as pure.ui.MovieEvent[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.ui.UITweenParameter[])){
			    pure.ui.UITweenParameter[] array = obj as pure.ui.UITweenParameter[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.ui.PRepeatImage_Dll.Direction[])){
			    pure.ui.PRepeatImage_Dll.Direction[] array = obj as pure.ui.PRepeatImage_Dll.Direction[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector2[])){
			    UnityEngine.Vector2[] array = obj as UnityEngine.Vector2[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector3[])){
			    UnityEngine.Vector3[] array = obj as UnityEngine.Vector3[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Vector4[])){
			    UnityEngine.Vector4[] array = obj as UnityEngine.Vector4[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Color[])){
			    UnityEngine.Color[] array = obj as UnityEngine.Color[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Quaternion[])){
			    UnityEngine.Quaternion[] array = obj as UnityEngine.Quaternion[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Ray[])){
			    UnityEngine.Ray[] array = obj as UnityEngine.Ray[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Bounds[])){
			    UnityEngine.Bounds[] array = obj as UnityEngine.Bounds[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(UnityEngine.Ray2D[])){
			    UnityEngine.Ray2D[] array = obj as UnityEngine.Ray2D[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.EasePathType[])){
			    pure.utils.tween.EasePathType[] array = obj as pure.utils.tween.EasePathType[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenTargetMode[])){
			    pure.utils.tween.TweenTargetMode[] array = obj as pure.utils.tween.TweenTargetMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.utils.tween.TweenDirection[])){
			    pure.utils.tween.TweenDirection[] array = obj as pure.utils.tween.TweenDirection[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(pure.ui.NumberField_Dll.NumberChangeMode[])){
			    pure.ui.NumberField_Dll.NumberChangeMode[] array = obj as pure.ui.NumberField_Dll.NumberChangeMode[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.mono.range.SightData[])){
			    game.mono.range.SightData[] array = obj as game.mono.range.SightData[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
			else if (type == typeof(game.ai.machine.vo.DamageCalc[])){
			    game.ai.machine.vo.DamageCalc[] array = obj as game.ai.machine.vo.DamageCalc[];
				p.Get(L, obj_idx, out array[array_idx]);
				return true;
			}
            return false;
		}
	}
}