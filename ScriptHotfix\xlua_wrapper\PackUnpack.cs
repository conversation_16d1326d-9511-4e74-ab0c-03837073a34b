﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using System;


namespace XLua
{
    public static class PackUnpack
    {
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ui.ScrollVO val)
		{
		    val = new game.ui.ScrollVO();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "min"))
            {
			    
                translator.Get(L, top + 1, out val.min);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "size"))
            {
			    
                translator.Get(L, top + 1, out val.size);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ui.ScrollVO field)
        {
            
            if(!cbv.Pack(buff, offset, field.min))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.size))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ui.ScrollVO field)
        {
            field = default(game.ui.ScrollVO);
            
            if(!cbv.UnPack(buff, offset, out field.min))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.size))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.mono.avatar.AvatarCondition val)
		{
		    val = new game.mono.avatar.AvatarCondition();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "name"))
            {
			    
                translator.Get(L, top + 1, out val.name);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.mono.avatar.AvatarCondition field)
        {
            
            if(!cbv.Pack(buff, offset, field.name))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.value))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.mono.avatar.AvatarCondition field)
        {
            field = default(game.mono.avatar.AvatarCondition);
            
            if(!cbv.UnPack(buff, offset, out field.name))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.value))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.entity.OperationVO val)
		{
		    val = new game.ai.entity.OperationVO();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "code"))
            {
			    
                translator.Get(L, top + 1, out val.code);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "slot"))
            {
			    
                translator.Get(L, top + 1, out val.slot);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "action"))
            {
			    
                translator.Get(L, top + 1, out val.action);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "world"))
            {
			    
                translator.Get(L, top + 1, out val.world);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.entity.OperationVO field)
        {
            
            if(!cbv.Pack(buff, offset, field.code))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.slot))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.action))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.world))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.entity.OperationVO field)
        {
            field = default(game.ai.entity.OperationVO);
            
            if(!cbv.UnPack(buff, offset, out field.code))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.slot))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.action))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.world))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Vector3 val)
		{
		    val = new UnityEngine.Vector3();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "x"))
            {
			    
                translator.Get(L, top + 1, out val.x);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "y"))
            {
			    
                translator.Get(L, top + 1, out val.y);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "z"))
            {
			    
                translator.Get(L, top + 1, out val.z);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Vector3 field)
        {
            
            if(!LuaAPI.xlua_pack_float3(buff, offset, field.x, field.y, field.z))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Vector3 field)
        {
            field = default(UnityEngine.Vector3);
            
            float x = default(float);
            float y = default(float);
            float z = default(float);
            
            if(!LuaAPI.xlua_unpack_float3(buff, offset, out x, out y, out z))
            {
                return false;
            }
            field.x = x;
            field.y = y;
            field.z = z;
            
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.entity.avatar.RenderSlot val)
		{
		    val = new game.ai.entity.avatar.RenderSlot();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "index"))
            {
			    
                translator.Get(L, top + 1, out val.index);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "display"))
            {
			    
                translator.Get(L, top + 1, out val.display);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "bone"))
            {
			    
                translator.Get(L, top + 1, out val.bone);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "color"))
            {
			    
                translator.Get(L, top + 1, out val.color);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "prefab"))
            {
			    
                translator.Get(L, top + 1, out val.prefab);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.entity.avatar.RenderSlot field)
        {
            
            if(!cbv.Pack(buff, offset, field.index))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.display))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.bone))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.color))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.prefab))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.entity.avatar.RenderSlot field)
        {
            field = default(game.ai.entity.avatar.RenderSlot);
            
            if(!cbv.UnPack(buff, offset, out field.index))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.display))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.bone))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.color))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.prefab))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out pure.utils.mathTools.HashCode val)
		{
		    val = new pure.utils.mathTools.HashCode();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "m0"))
            {
			    
                translator.Get(L, top + 1, out val.m0);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "m1"))
            {
			    
                translator.Get(L, top + 1, out val.m1);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "m2"))
            {
			    
                translator.Get(L, top + 1, out val.m2);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "m3"))
            {
			    
                translator.Get(L, top + 1, out val.m3);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, pure.utils.mathTools.HashCode field)
        {
            
            if(!cbv.Pack(buff, offset, field.m0))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.m1))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.m2))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.m3))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out pure.utils.mathTools.HashCode field)
        {
            field = default(pure.utils.mathTools.HashCode);
            
            if(!cbv.UnPack(buff, offset, out field.m0))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.m1))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.m2))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.m3))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.personality.NpcPropertyUnit val)
		{
		    val = new game.ai.personality.NpcPropertyUnit();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "field"))
            {
			    
                translator.Get(L, top + 1, out val.field);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.personality.NpcPropertyUnit field)
        {
            
            if(!cbv.Pack(buff, offset, field.field))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.value))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.personality.NpcPropertyUnit field)
        {
            field = default(game.ai.personality.NpcPropertyUnit);
            
            if(!cbv.UnPack(buff, offset, out field.field))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.value))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.AnimVO val)
		{
		    val = new game.ai.machine.vo.AnimVO();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "type"))
            {
			    
                translator.Get(L, top + 1, out val.type);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "name"))
            {
			    
                translator.Get(L, top + 1, out val.name);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "duration"))
            {
			    
                translator.Get(L, top + 1, out val.duration);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.AnimVO field)
        {
            
            if(!cbv.Pack(buff, offset, field.type))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.name))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.value))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.duration))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.AnimVO field)
        {
            field = default(game.ai.machine.vo.AnimVO);
            
            if(!cbv.UnPack(buff, offset, out field.type))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.name))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.value))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.duration))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.SkillCeoffModifier val)
		{
		    val = new game.ai.machine.vo.SkillCeoffModifier();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "mode"))
            {
			    
                translator.Get(L, top + 1, out val.mode);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.SkillCeoffModifier field)
        {
            
            if(!cbv.Pack(buff, offset, field.mode))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.value))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.SkillCeoffModifier field)
        {
            field = default(game.ai.machine.vo.SkillCeoffModifier);
            
            if(!cbv.UnPack(buff, offset, out field.mode))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.value))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.SkillRetrieverInfo val)
		{
		    val = new game.ai.machine.vo.SkillRetrieverInfo();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "entityId"))
            {
			    
                translator.Get(L, top + 1, out val.entityId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "targetId"))
            {
			    
                translator.Get(L, top + 1, out val.targetId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "enterAttackTime"))
            {
			    
                translator.Get(L, top + 1, out val.enterAttackTime);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "distance"))
            {
			    
                translator.Get(L, top + 1, out val.distance);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.SkillRetrieverInfo field)
        {
            
            if(!cbv.Pack(buff, offset, field.entityId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.targetId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.enterAttackTime))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.distance))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.SkillRetrieverInfo field)
        {
            field = default(game.ai.machine.vo.SkillRetrieverInfo);
            
            if(!cbv.UnPack(buff, offset, out field.entityId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.targetId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.enterAttackTime))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.distance))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.FxBuffVO val)
		{
		    val = new game.ai.machine.vo.FxBuffVO();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "hostId"))
            {
			    
                translator.Get(L, top + 1, out val.hostId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "targetId"))
            {
			    
                translator.Get(L, top + 1, out val.targetId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "buffId"))
            {
			    
                translator.Get(L, top + 1, out val.buffId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "duration"))
            {
			    
                translator.Get(L, top + 1, out val.duration);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "maxOverlay"))
            {
			    
                translator.Get(L, top + 1, out val.maxOverlay);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.FxBuffVO field)
        {
            
            if(!cbv.Pack(buff, offset, field.hostId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.targetId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.buffId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.duration))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.maxOverlay))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.FxBuffVO field)
        {
            field = default(game.ai.machine.vo.FxBuffVO);
            
            if(!cbv.UnPack(buff, offset, out field.hostId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.targetId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.buffId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.duration))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.maxOverlay))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.ComboVO val)
		{
		    val = new game.ai.machine.vo.ComboVO();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "type"))
            {
			    
                translator.Get(L, top + 1, out val.type);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "duration"))
            {
			    
                translator.Get(L, top + 1, out val.duration);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "overlap"))
            {
			    
                translator.Get(L, top + 1, out val.overlap);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.ComboVO field)
        {
            
            if(!cbv.Pack(buff, offset, field.type))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.duration))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.overlap))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.ComboVO field)
        {
            field = default(game.ai.machine.vo.ComboVO);
            
            if(!cbv.UnPack(buff, offset, out field.type))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.duration))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.overlap))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.machine.vo.FightResult val)
		{
		    val = new game.ai.machine.vo.FightResult();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "code"))
            {
			    
                translator.Get(L, top + 1, out val.code);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "stunSpeed"))
            {
			    
                translator.Get(L, top + 1, out val.stunSpeed);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.machine.vo.FightResult field)
        {
            
            if(!cbv.Pack(buff, offset, field.code))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 4, field.stunSpeed))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.machine.vo.FightResult field)
        {
            field = default(game.ai.machine.vo.FightResult);
            
            if(!cbv.UnPack(buff, offset, out field.code))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 4, out field.stunSpeed))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_AIMode val)
		{
		    val = new game.ai.client.Action_AIMode();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "time"))
            {
			    
                translator.Get(L, top + 1, out val.time);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "mode"))
            {
			    
                translator.Get(L, top + 1, out val.mode);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_AIMode field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.time))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.mode))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_AIMode field)
        {
            field = default(game.ai.client.Action_AIMode);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.time))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.mode))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_Animation val)
		{
		    val = new game.ai.client.Action_Animation();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "time"))
            {
			    
                translator.Get(L, top + 1, out val.time);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "type"))
            {
			    
                translator.Get(L, top + 1, out val.type);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "name"))
            {
			    
                translator.Get(L, top + 1, out val.name);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_Animation field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.time))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.type))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.name))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 32, field.value))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_Animation field)
        {
            field = default(game.ai.client.Action_Animation);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.time))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.type))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.name))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 32, out field.value))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_AutoMode val)
		{
		    val = new game.ai.client.Action_AutoMode();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "mode"))
            {
			    
                translator.Get(L, top + 1, out val.mode);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_AutoMode field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.mode))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_AutoMode field)
        {
            field = default(game.ai.client.Action_AutoMode);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.mode))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_BuffControl val)
		{
		    val = new game.ai.client.Action_BuffControl();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "mode"))
            {
			    
                translator.Get(L, top + 1, out val.mode);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_BuffControl field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.mode))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_BuffControl field)
        {
            field = default(game.ai.client.Action_BuffControl);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.mode))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_BuffShow val)
		{
		    val = new game.ai.client.Action_BuffShow();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "duetime"))
            {
			    
                translator.Get(L, top + 1, out val.duetime);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "buffId"))
            {
			    
                translator.Get(L, top + 1, out val.buffId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "bone"))
            {
			    
                translator.Get(L, top + 1, out val.bone);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "prefab"))
            {
			    
                translator.Get(L, top + 1, out val.prefab);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_BuffShow field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.duetime))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.buffId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 32, field.bone))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.prefab))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_BuffShow field)
        {
            field = default(game.ai.client.Action_BuffShow);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.duetime))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.buffId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 32, out field.bone))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.prefab))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_BulletAdd val)
		{
		    val = new game.ai.client.Action_BulletAdd();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "index"))
            {
			    
                translator.Get(L, top + 1, out val.index);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "position"))
            {
			    
                translator.Get(L, top + 1, out val.position);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "rotate"))
            {
			    
                translator.Get(L, top + 1, out val.rotate);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "velocity"))
            {
			    
                translator.Get(L, top + 1, out val.velocity);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "prefab"))
            {
			    
                translator.Get(L, top + 1, out val.prefab);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "bindscreen"))
            {
			    
                translator.Get(L, top + 1, out val.bindscreen);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_BulletAdd field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.index))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.position))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.rotate))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.velocity))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 48, field.prefab))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 64, field.bindscreen))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_BulletAdd field)
        {
            field = default(game.ai.client.Action_BulletAdd);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.index))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.position))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.rotate))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.velocity))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 48, out field.prefab))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 64, out field.bindscreen))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_BulletTransform val)
		{
		    val = new game.ai.client.Action_BulletTransform();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "index"))
            {
			    
                translator.Get(L, top + 1, out val.index);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "position"))
            {
			    
                translator.Get(L, top + 1, out val.position);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "rotate"))
            {
			    
                translator.Get(L, top + 1, out val.rotate);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "bindscreen"))
            {
			    
                translator.Get(L, top + 1, out val.bindscreen);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_BulletTransform field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.index))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.position))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.rotate))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.bindscreen))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_BulletTransform field)
        {
            field = default(game.ai.client.Action_BulletTransform);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.index))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.position))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.rotate))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.bindscreen))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_BulletBind val)
		{
		    val = new game.ai.client.Action_BulletBind();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "bone"))
            {
			    
                translator.Get(L, top + 1, out val.bone);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "target"))
            {
			    
                translator.Get(L, top + 1, out val.target);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "orient"))
            {
			    
                translator.Get(L, top + 1, out val.orient);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "offset"))
            {
			    
                translator.Get(L, top + 1, out val.offset);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "rotate"))
            {
			    
                translator.Get(L, top + 1, out val.rotate);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_BulletBind field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.bone))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.target))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.orient))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.offset))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.rotate))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_BulletBind field)
        {
            field = default(game.ai.client.Action_BulletBind);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.bone))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.target))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.orient))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.offset))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.rotate))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_IOLock val)
		{
		    val = new game.ai.client.Action_IOLock();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "status"))
            {
			    
                translator.Get(L, top + 1, out val.status);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_IOLock field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.status))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.frame))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_IOLock field)
        {
            field = default(game.ai.client.Action_IOLock);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.status))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.frame))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_RemoveEntity val)
		{
		    val = new game.ai.client.Action_RemoveEntity();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "position"))
            {
			    
                translator.Get(L, top + 1, out val.position);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_RemoveEntity field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.position))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_RemoveEntity field)
        {
            field = default(game.ai.client.Action_RemoveEntity);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.position))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_ShaderSlot val)
		{
		    val = new game.ai.client.Action_ShaderSlot();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "time"))
            {
			    
                translator.Get(L, top + 1, out val.time);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "body"))
            {
			    
                translator.Get(L, top + 1, out val.body);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "type"))
            {
			    
                translator.Get(L, top + 1, out val.type);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "property"))
            {
			    
                translator.Get(L, top + 1, out val.property);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "value"))
            {
			    
                translator.Get(L, top + 1, out val.value);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_ShaderSlot field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.time))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.body))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.type))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 24, field.property))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 28, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.value))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_ShaderSlot field)
        {
            field = default(game.ai.client.Action_ShaderSlot);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.time))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.body))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.type))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 24, out field.property))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 28, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.value))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_SkillLocate val)
		{
		    val = new game.ai.client.Action_SkillLocate();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "skillId"))
            {
			    
                translator.Get(L, top + 1, out val.skillId);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "slot"))
            {
			    
                translator.Get(L, top + 1, out val.slot);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "prefab"))
            {
			    
                translator.Get(L, top + 1, out val.prefab);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "range"))
            {
			    
                translator.Get(L, top + 1, out val.range);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_SkillLocate field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.skillId))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.slot))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.prefab))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.range))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_SkillLocate field)
        {
            field = default(game.ai.client.Action_SkillLocate);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.skillId))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.slot))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.prefab))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.range))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Vector2 val)
		{
		    val = new UnityEngine.Vector2();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "x"))
            {
			    
                translator.Get(L, top + 1, out val.x);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "y"))
            {
			    
                translator.Get(L, top + 1, out val.y);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Vector2 field)
        {
            
            if(!LuaAPI.xlua_pack_float2(buff, offset, field.x, field.y))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Vector2 field)
        {
            field = default(UnityEngine.Vector2);
            
            float x = default(float);
            float y = default(float);
            
            if(!LuaAPI.xlua_unpack_float2(buff, offset, out x, out y))
            {
                return false;
            }
            field.x = x;
            field.y = y;
            
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_SpeakBubble val)
		{
		    val = new game.ai.client.Action_SpeakBubble();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "color"))
            {
			    
                translator.Get(L, top + 1, out val.color);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "word"))
            {
			    
                translator.Get(L, top + 1, out val.word);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "font"))
            {
			    
                translator.Get(L, top + 1, out val.font);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "prefab"))
            {
			    
                translator.Get(L, top + 1, out val.prefab);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "duetime"))
            {
			    
                translator.Get(L, top + 1, out val.duetime);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_SpeakBubble field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.color))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.word))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.font))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 20, field.prefab))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 36, field.duetime))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_SpeakBubble field)
        {
            field = default(game.ai.client.Action_SpeakBubble);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.color))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 12, out field.word))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.font))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 20, out field.prefab))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 36, out field.duetime))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_SpeedMulti val)
		{
		    val = new game.ai.client.Action_SpeedMulti();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "multi"))
            {
			    
                translator.Get(L, top + 1, out val.multi);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_SpeedMulti field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.multi))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_SpeedMulti field)
        {
            field = default(game.ai.client.Action_SpeedMulti);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.multi))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out game.ai.client.Action_Transform val)
		{
		    val = new game.ai.client.Action_Transform();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "id"))
            {
			    
                translator.Get(L, top + 1, out val.id);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "frame"))
            {
			    
                translator.Get(L, top + 1, out val.frame);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "position"))
            {
			    
                translator.Get(L, top + 1, out val.position);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "rotate"))
            {
			    
                translator.Get(L, top + 1, out val.rotate);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "mode"))
            {
			    
                translator.Get(L, top + 1, out val.mode);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, game.ai.client.Action_Transform field)
        {
            
            if(!cbv.Pack(buff, offset, field.id))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.frame))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 16, field.position))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 28, field.rotate))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 40, field.mode))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out game.ai.client.Action_Transform field)
        {
            field = default(game.ai.client.Action_Transform);
            
            if(!cbv.UnPack(buff, offset, out field.id))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 8, out field.frame))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 16, out field.position))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 28, out field.rotate))
            {
                return false;
            }
            
            if(!cbv.UnPack(buff, offset + 40, out field.mode))
            {
                return false;
            }
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Vector4 val)
		{
		    val = new UnityEngine.Vector4();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "x"))
            {
			    
                translator.Get(L, top + 1, out val.x);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "y"))
            {
			    
                translator.Get(L, top + 1, out val.y);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "z"))
            {
			    
                translator.Get(L, top + 1, out val.z);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "w"))
            {
			    
                translator.Get(L, top + 1, out val.w);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Vector4 field)
        {
            
            if(!LuaAPI.xlua_pack_float4(buff, offset, field.x, field.y, field.z, field.w))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Vector4 field)
        {
            field = default(UnityEngine.Vector4);
            
            float x = default(float);
            float y = default(float);
            float z = default(float);
            float w = default(float);
            
            if(!LuaAPI.xlua_unpack_float4(buff, offset, out x, out y, out z, out w))
            {
                return false;
            }
            field.x = x;
            field.y = y;
            field.z = z;
            field.w = w;
            
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Color val)
		{
		    val = new UnityEngine.Color();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "r"))
            {
			    
                translator.Get(L, top + 1, out val.r);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "g"))
            {
			    
                translator.Get(L, top + 1, out val.g);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "b"))
            {
			    
                translator.Get(L, top + 1, out val.b);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "a"))
            {
			    
                translator.Get(L, top + 1, out val.a);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Color field)
        {
            
            if(!LuaAPI.xlua_pack_float4(buff, offset, field.r, field.g, field.b, field.a))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Color field)
        {
            field = default(UnityEngine.Color);
            
            float r = default(float);
            float g = default(float);
            float b = default(float);
            float a = default(float);
            
            if(!LuaAPI.xlua_unpack_float4(buff, offset, out r, out g, out b, out a))
            {
                return false;
            }
            field.r = r;
            field.g = g;
            field.b = b;
            field.a = a;
            
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Quaternion val)
		{
		    val = new UnityEngine.Quaternion();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "x"))
            {
			    
                translator.Get(L, top + 1, out val.x);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "y"))
            {
			    
                translator.Get(L, top + 1, out val.y);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "z"))
            {
			    
                translator.Get(L, top + 1, out val.z);
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "w"))
            {
			    
                translator.Get(L, top + 1, out val.w);
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Quaternion field)
        {
            
            if(!LuaAPI.xlua_pack_float4(buff, offset, field.x, field.y, field.z, field.w))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Quaternion field)
        {
            field = default(UnityEngine.Quaternion);
            
            float x = default(float);
            float y = default(float);
            float z = default(float);
            float w = default(float);
            
            if(!LuaAPI.xlua_unpack_float4(buff, offset, out x, out y, out z, out w))
            {
                return false;
            }
            field.x = x;
            field.y = y;
            field.z = z;
            field.w = w;
            
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Ray val)
		{
		    val = new UnityEngine.Ray();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "origin"))
            {
			    
				var origin = val.origin;
				translator.Get(L, top + 1, out origin);
				val.origin = origin;
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "direction"))
            {
			    
				var direction = val.direction;
				translator.Get(L, top + 1, out direction);
				val.direction = direction;
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Ray field)
        {
            
            if(!cbv.Pack(buff, offset, field.origin))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.direction))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Ray field)
        {
            field = default(UnityEngine.Ray);
            
            var origin = field.origin;
            if(!cbv.UnPack(buff, offset, out origin))
            {
                return false;
            }
            field.origin = origin;
            
            var direction = field.direction;
            if(!cbv.UnPack(buff, offset + 12, out direction))
            {
                return false;
            }
            field.direction = direction;
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Bounds val)
		{
		    val = new UnityEngine.Bounds();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "center"))
            {
			    
				var center = val.center;
				translator.Get(L, top + 1, out center);
				val.center = center;
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "extents"))
            {
			    
				var extents = val.extents;
				translator.Get(L, top + 1, out extents);
				val.extents = extents;
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Bounds field)
        {
            
            if(!cbv.Pack(buff, offset, field.center))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 12, field.extents))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Bounds field)
        {
            field = default(UnityEngine.Bounds);
            
            var center = field.center;
            if(!cbv.UnPack(buff, offset, out center))
            {
                return false;
            }
            field.center = center;
            
            var extents = field.extents;
            if(!cbv.UnPack(buff, offset + 12, out extents))
            {
                return false;
            }
            field.extents = extents;
            
            return true;
        }
        
		
		public static void UnPack(this CopyByValue cbv, ObjectTranslator translator, RealStatePtr L, int idx, out UnityEngine.Ray2D val)
		{
		    val = new UnityEngine.Ray2D();
            int top = LuaAPI.lua_gettop(L);
			
			if (Utils.LoadField(L, idx, "origin"))
            {
			    
				var origin = val.origin;
				translator.Get(L, top + 1, out origin);
				val.origin = origin;
				
            }
            LuaAPI.lua_pop(L, 1);
			
			if (Utils.LoadField(L, idx, "direction"))
            {
			    
				var direction = val.direction;
				translator.Get(L, top + 1, out direction);
				val.direction = direction;
				
            }
            LuaAPI.lua_pop(L, 1);
			
		}
		
        public static bool Pack(this CopyByValue cbv, IntPtr buff, int offset, UnityEngine.Ray2D field)
        {
            
            if(!cbv.Pack(buff, offset, field.origin))
            {
                return false;
            }
            
            if(!cbv.Pack(buff, offset + 8, field.direction))
            {
                return false;
            }
            
            return true;
        }
        public static bool UnPack(this CopyByValue cbv, IntPtr buff, int offset, out UnityEngine.Ray2D field)
        {
            field = default(UnityEngine.Ray2D);
            
            var origin = field.origin;
            if(!cbv.UnPack(buff, offset, out origin))
            {
                return false;
            }
            field.origin = origin;
            
            var direction = field.direction;
            if(!cbv.UnPack(buff, offset + 8, out direction))
            {
                return false;
            }
            field.direction = direction;
            
            return true;
        }
        
    }
}