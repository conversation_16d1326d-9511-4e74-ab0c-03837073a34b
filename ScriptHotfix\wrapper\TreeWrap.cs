﻿//this source code was auto-generated, do not modify it
using System.Collections.Generic;
using pure.database.structure.tree;
using dl.ai.scene;
using game.ai.firefx.server;
using game.ai.firefx.io;
using game.ai.firefx.hit;
using game.ai.firefx.events;
using game.ai.firefx.core;
using game.ai.firefx.condition;
using game.ai.firefx.bullet;
using game.ai.firefx.bullet.move;
using game.ai.firefx.bullet.emit;
using game.ai.firefx.bullet.capture;
using game.ai.firefx.bullet.area;
using game.ai.firefx.action;
using game.ai.firefx.action.defense;
using game.ai.scenetree;
using game.ai.personality;
using game.ai.personality.each;
using game.ai.personality.condition;
using game.ai.personality.actions;
using game.ai.dialog;
using game.mediator.setting;
using pure.refactor.serialize;
using UnityEngine;
using pure.utils.enumeration;
using pure.utils.timeScale;
using game.ai.machine.vo;
using game.ai.entity;
using pure.utils.tween;
using game.ai.firefx.enums;
using game.ai.entity.handler.dnf;
using game.mono.range;
using pure.utils.animation;
using game.ai.runner;
using game.mono.data;
using pure.behavior;
using pure.physic;
using game.mono.fx;
using game.lua;
using pure.utils.color;
using game.mediator.enums;
using game.mono.manager;
using pure.assetdb;
namespace registerWrap {
	public class TreeWrap{

        private static bool register;
        public static void Init(){
            if(!register){  
                new TreeWrap().__reg__();
            }
            register = true;
        }
		private void __reg__(){
			TreeFactory.Reset();
			TreeFactory.Register(504239274,()=>new SceneAct_HudShowHide(),__sceneact_hudshowhide_reader__);
			TreeFactory.Register(-619951625,()=>new SceneAct_PlayOffsetBullet(),__sceneact_playoffsetbullet_reader__);
			TreeFactory.Register(-1158382475,()=>new SceneAct_PlaySkeletonAnimation(),__sceneact_playskeletonanimation_reader__);
			TreeFactory.Register(-663963976,()=>new SceneCon_FieldNumber(),__scenecon_fieldnumber_reader__);
			TreeFactory.Register(822229567,()=>new SceneNpc_ClosePanel(),__scenenpc_closepanel_reader__);
			TreeFactory.Register(212766450,()=>new SceneNpc_RemoveShadow(),__scenenpc_removeshadow_reader__);
			TreeFactory.Register(1718466712,()=>new ScenePlot_Announce(),__sceneplot_announce_reader__);
			TreeFactory.Register(121097174,()=>new FireFxServer_NotifyCollect(),__firefxserver_notifycollect_reader__);
			TreeFactory.Register(1557132533,()=>new FireFxServer_NotifyPicker(),__firefxserver_notifypicker_reader__);
			TreeFactory.Register(601326598,()=>new FireFxAct_ControlMove(),__firefxact_controlmove_reader__);
			TreeFactory.Register(-1886079174,()=>new FireFxAct_ControlOrient(),__firefxact_controlorient_reader__);
			TreeFactory.Register(1314173063,()=>new FireFxAct_HoldKey(),__firefxact_holdkey_reader__);
			TreeFactory.Register(1018372925,()=>new FireFxAct_IOMoveWatcher(),__firefxact_iomovewatcher_reader__);
			TreeFactory.Register(-26613829,()=>new FireFxAct_PressKey(),__firefxact_presskey_reader__);
			TreeFactory.Register(1800031945,()=>new FireFxAct_SetCombo(),__firefxact_setcombo_reader__);
			TreeFactory.Register(593244889,()=>new FireFxHit_AddBuff(),__firefxhit_addbuff_reader__);
			TreeFactory.Register(-724629869,()=>new FireFxHit_AddHitPower(),__firefxhit_addhitpower_reader__);
			TreeFactory.Register(511864564,()=>new FireFxHit_Break(),__firefxhit_break_reader__);
			TreeFactory.Register(508632871,()=>new FireFxHit_Buff(),__firefxhit_buff_reader__);
			TreeFactory.Register(2004220156,()=>new FireFxHit_CheckHitTarget(),__firefxhit_checkhittarget_reader__);
			TreeFactory.Register(-791087805,()=>new FireFxHit_Damage(),__firefxhit_damage_reader__);
			TreeFactory.Register(-1784705145,()=>new FireFxHit_End(),__firefxhit_end_reader__);
			TreeFactory.Register(508604866,()=>new FireFxHit_HitAway(),__firefxhit_hitaway_reader__);
			TreeFactory.Register(-1784706104,()=>new FireFxHit_HitDnf(),__firefxhit_hitdnf_reader__);
			TreeFactory.Register(-471390227,()=>new FireFxHit_HitPull(),__firefxhit_hitpull_reader__);
			TreeFactory.Register(-1399313190,()=>new FireFxHit_HitPunch(),null);
			TreeFactory.Register(-471390014,()=>new FireFxHit_HitPush(),__firefxhit_hitpush_reader__);
			TreeFactory.Register(52056837,()=>new FireFxHit_IterTargets(),__firefxhit_itertargets_reader__);
			TreeFactory.Register(-1454253086,()=>new FireFxHit_RuntimeProperty_Add(),__firefxhit_runtimeproperty_add_reader__);
			TreeFactory.Register(159085121,()=>new FireFxHit_RuntimeProperty_Set(),__firefxhit_runtimeproperty_set_reader__);
			TreeFactory.Register(1748255454,()=>new FireFxHit_ScreenTargetRotate(),__firefxhit_screentargetrotate_reader__);
			TreeFactory.Register(-2143883994,()=>new FireFxHit_SetFlyText(),__firefxhit_setflytext_reader__);
			TreeFactory.Register(-268513910,()=>new FireFxHit_SetHitPhase(),__firefxhit_sethitphase_reader__);
			TreeFactory.Register(1049108314,()=>new FireFxHit_SetSkillCeoff(),__firefxhit_setskillceoff_reader__);
			TreeFactory.Register(-1073279680,()=>new FireFxHit_TriggerPropBuff(),__firefxhit_triggerpropbuff_reader__);
			TreeFactory.Register(-163731220,()=>new FireFxHit_CallBuff(),__firefxhit_callbuff_reader__);
			TreeFactory.Register(-1971320061,()=>new FireFxHit_CallFightEvent(),__firefxhit_callfightevent_reader__);
			TreeFactory.Register(-729199358,()=>new FirefxComposite_Sequence(),__firefxcomposite_sequence_reader__);
			TreeFactory.Register(-1273800313,()=>new FirefxComposite_SequenceSelector(),__firefxcomposite_sequenceselector_reader__);
			TreeFactory.Register(-1293970513,()=>new FirefxComposite_Parallel(),__firefxcomposite_parallel_reader__);
			TreeFactory.Register(1430572206,()=>new FirefxComposite_ParallelSelector(),__firefxcomposite_parallelselector_reader__);
			TreeFactory.Register(-471282571,()=>new FirefxDeco_Time(),__firefxdeco_time_reader__);
			TreeFactory.Register(-1995598741,()=>new FirefxDeco_Random(),__firefxdeco_random_reader__);
			TreeFactory.Register(-1991844541,()=>new FirefxDeco_Repeat(),__firefxdeco_repeat_reader__);
			TreeFactory.Register(1037917690,()=>new FirefxDeco_UntilSuccess(),__firefxdeco_untilsuccess_reader__);
			TreeFactory.Register(-1403767308,()=>new FirefxDeco_UntilFailure(),__firefxdeco_untilfailure_reader__);
			TreeFactory.Register(1584548221,()=>new FirefxDeco_NeverStop(),__firefxdeco_neverstop_reader__);
			TreeFactory.Register(-471336310,()=>new FireFxRoot(),null);
			TreeFactory.Register(617831217,()=>new FireFxCon_ActionIndex(),__firefxcon_actionindex_reader__);
			TreeFactory.Register(-360162275,()=>new FireFxCon_CheckComboTime(),__firefxcon_checkcombotime_reader__);
			TreeFactory.Register(747603975,()=>new FireFxCon_CheckHitIndex(),__firefxcon_checkhitindex_reader__);
			TreeFactory.Register(2065588011,()=>new FireFxCon_CheckHitQty(),__firefxcon_checkhitqty_reader__);
			TreeFactory.Register(-998294018,()=>new FireFxCon_CheckMonsterType(),__firefxcon_checkmonstertype_reader__);
			TreeFactory.Register(-723612138,()=>new FireFxCon_CountSurround(),__firefxcon_countsurround_reader__);
			TreeFactory.Register(1841158571,()=>new FireFxCon_HoldKeyTime(),__firefxcon_holdkeytime_reader__);
			TreeFactory.Register(1975603520,()=>new FireFxCon_IsCritical(),__firefxcon_iscritical_reader__);
			TreeFactory.Register(-485418062,()=>new FireFxCon_IsDoubleHit(),__firefxcon_isdoublehit_reader__);
			TreeFactory.Register(-630025995,()=>new FireFxCon_IsHost(),__firefxcon_ishost_reader__);
			TreeFactory.Register(976178175,()=>new FireFxCon_IsKillHit(),__firefxcon_iskillhit_reader__);
			TreeFactory.Register(-2069923621,()=>new FireFxCon_OnHit(),__firefxcon_onhit_reader__);
			TreeFactory.Register(2083486980,()=>new FireFxCon_RemoveTarget(),__firefxcon_removetarget_reader__);
			TreeFactory.Register(-2100785488,()=>new FireFxCon_SearchSummon(),__firefxcon_searchsummon_reader__);
			TreeFactory.Register(-2025357284,()=>new FireFxCon_SearchTarget(),__firefxcon_searchtarget_reader__);
			TreeFactory.Register(831118168,()=>new FireFxCon_TimeToPrevFireTime(),__firefxcon_timetoprevfiretime_reader__);
			TreeFactory.Register(1102828951,()=>new BulletAct_AnimationMove(),__bulletact_animationmove_reader__);
			TreeFactory.Register(1128160128,()=>new BulletAct_BindBone(),__bulletact_bindbone_reader__);
			TreeFactory.Register(1128377202,()=>new BulletAct_Bounce(),__bulletact_bounce_reader__);
			TreeFactory.Register(-17260593,()=>new BulletAct_BuffArea(),__bulletact_buffarea_reader__);
			TreeFactory.Register(115860869,()=>new BulletAct_Chain(),__bulletact_chain_reader__);
			TreeFactory.Register(1499093140,()=>new BulletAct_Collide(),__bulletact_collide_reader__);
			TreeFactory.Register(1159288294,()=>new BulletAct_Create(),__bulletact_create_reader__);
			TreeFactory.Register(-885532109,()=>new BulletAct_CreateTrap(),__bulletact_createtrap_reader__);
			TreeFactory.Register(1821992535,()=>new BulletAct_DisposeWithRunner(),__bulletact_disposewithrunner_reader__);
			TreeFactory.Register(996952815,()=>new BulletAct_Dissolve(),__bulletact_dissolve_reader__);
			TreeFactory.Register(-633356571,()=>new BulletAct_Fall(),null);
			TreeFactory.Register(-241632851,()=>new BulletAct_FetchTarget(),__bulletact_fetchtarget_reader__);
			TreeFactory.Register(1414486044,()=>new BulletAct_LookAt(),__bulletact_lookat_reader__);
			TreeFactory.Register(-633048418,()=>new BulletAct_Play(),__bulletact_play_reader__);
			TreeFactory.Register(511515399,()=>new BulletAct_StartLighten(),__bulletact_startlighten_reader__);
			TreeFactory.Register(1854360641,()=>new BulletAct_Tween(),__bulletact_tween_reader__);
			TreeFactory.Register(1854364886,()=>new BulletAct_Twirl(),__bulletact_twirl_reader__);
			TreeFactory.Register(-633170330,()=>new BulletCon_Life(),__bulletcon_life_reader__);
			TreeFactory.Register(-1769433037,()=>new BulletCon_ScreenSeekTarget(),__bulletcon_screenseektarget_reader__);
			TreeFactory.Register(-832803686,()=>new BulletCon_SearchTarget(),__bulletcon_searchtarget_reader__);
			TreeFactory.Register(-436072503,()=>new BulletAct_Fly(),__bulletact_fly_reader__);
			TreeFactory.Register(1240247407,()=>new BulletAct_FlySin(),__bulletact_flysin_reader__);
			TreeFactory.Register(1242624155,()=>new BulletAct_Follow(),__bulletact_follow_reader__);
			TreeFactory.Register(1030213836,()=>new BulletAct_Parabola(),__bulletact_parabola_reader__);
			TreeFactory.Register(-632965662,()=>new BulletAct_Seek(),__bulletact_seek_reader__);
			TreeFactory.Register(2090883532,()=>new BulletEmitter_Bounce(),__bulletemitter_bounce_reader__);
			TreeFactory.Register(1816990776,()=>new BulletEmitter_Line(),__bulletemitter_line_reader__);
			TreeFactory.Register(-1860624341,()=>new BulletEmitter_Normal(),__bulletemitter_normal_reader__);
			TreeFactory.Register(1556260198,()=>new BulletAct_CaptureRound(),__bulletact_captureround_reader__);
			TreeFactory.Register(-1875985579,()=>new BulletCapture_AddBuff(),__bulletcapture_addbuff_reader__);
			TreeFactory.Register(822301416,()=>new BulletCapture_LockSlot(),__bulletcapture_lockslot_reader__);
			TreeFactory.Register(-1008754678,()=>new BulletCapture_SpeedSlow(),__bulletcapture_speedslow_reader__);
			TreeFactory.Register(597118466,()=>new BulletPosition_Area(),__bulletposition_area_reader__);
			TreeFactory.Register(1257837775,()=>new BulletPosition_DividSpace(),__bulletposition_dividspace_reader__);
			TreeFactory.Register(1447218993,()=>new BulletPosition_HostPosition(),__bulletposition_hostposition_reader__);
			TreeFactory.Register(597437801,()=>new BulletPosition_Line(),__bulletposition_line_reader__);
			TreeFactory.Register(-1374498216,()=>new BulletPosition_ResetDestiny(),__bulletposition_resetdestiny_reader__);
			TreeFactory.Register(597616549,()=>new BulletPosition_Ring(),__bulletposition_ring_reader__);
			TreeFactory.Register(-1192905567,()=>new BulletPosition_Screen(),__bulletposition_screen_reader__);
			TreeFactory.Register(810596901,()=>new BulletPosition_SearchInSight(),__bulletposition_searchinsight_reader__);
			TreeFactory.Register(-1191490661,()=>new BulletPosition_Sector(),__bulletposition_sector_reader__);
			TreeFactory.Register(-471843911,()=>new FireFxAct_Animation(),__firefxact_animation_reader__);
			TreeFactory.Register(-1392765515,()=>new FireFxAct_BreakableWait(),__firefxact_breakablewait_reader__);
			TreeFactory.Register(2050862758,()=>new FireFxAct_CalcAnimIndex(),__firefxact_calcanimindex_reader__);
			TreeFactory.Register(-1963805,()=>new FireFxAct_ChangeWeapon(),__firefxact_changeweapon_reader__);
			TreeFactory.Register(-452690646,()=>new FireFxAct_ChaseMove(),__firefxact_chasemove_reader__);
			TreeFactory.Register(1267074394,()=>new FireFxAct_ClearCdOnSlot(),__firefxact_clearcdonslot_reader__);
			TreeFactory.Register(905810049,()=>new FireFxAct_ClearPrevSkillCD(),__firefxact_clearprevskillcd_reader__);
			TreeFactory.Register(-1084438165,()=>new FireFxAct_ClearTargets(),__firefxact_cleartargets_reader__);
			TreeFactory.Register(86223015,()=>new FireFxAct_CorrectOrient(),__firefxact_correctorient_reader__);
			TreeFactory.Register(-919747441,()=>new FireFxAct_DispatchEvent(),__firefxact_dispatchevent_reader__);
			TreeFactory.Register(-1715730810,()=>new FireFxAct_FetchContinueSkill(),__firefxact_fetchcontinueskill_reader__);
			TreeFactory.Register(1314766512,()=>new FireFxAct_FireSkill(),__firefxact_fireskill_reader__);
			TreeFactory.Register(29702485,()=>new FireFxAct_GetAnimIndex(),__firefxact_getanimindex_reader__);
			TreeFactory.Register(1769334476,()=>new FireFxAct_GlobalTimeScale(),__firefxact_globaltimescale_reader__);
			TreeFactory.Register(-809627152,()=>new FireFxAct_LimitAttackOrient(),__firefxact_limitattackorient_reader__);
			TreeFactory.Register(2140558042,()=>new FireFxAct_LookAt(),__firefxact_lookat_reader__);
			TreeFactory.Register(1459791115,()=>new FireFxAct_LookAtTarget(),__firefxact_lookattarget_reader__);
			TreeFactory.Register(-1709883790,()=>new FireFxAct_MarkCDClearFlag(),__firefxact_markcdclearflag_reader__);
			TreeFactory.Register(-471485063,()=>new FireFxAct_Move(),__firefxact_move_reader__);
			TreeFactory.Register(1079178373,()=>new FireFxAct_MoveToTarget(),__firefxact_movetotarget_reader__);
			TreeFactory.Register(399017599,()=>new FireFxAct_NpcSetInsteractive(),__firefxact_npcsetinsteractive_reader__);
			TreeFactory.Register(-1229204513,()=>new FireFxAct_RadialBlur(),__firefxact_radialblur_reader__);
			TreeFactory.Register(1620014948,()=>new FireFxAct_RandomCurve(),__firefxact_randomcurve_reader__);
			TreeFactory.Register(-73520986,()=>new FireFxAct_SetMode(),__firefxact_setmode_reader__);
			TreeFactory.Register(-1463702973,()=>new FireFxAct_SetSkillExtraCD(),__firefxact_setskillextracd_reader__);
			TreeFactory.Register(-2066959181,()=>new FireFxAct_SetTargetLimite(),__firefxact_settargetlimite_reader__);
			TreeFactory.Register(-1738347155,()=>new FireFxAct_ShakesScreen(),__firefxact_shakesscreen_reader__);
			TreeFactory.Register(-1725594521,()=>new FireFxAct_Sound(),__firefxact_sound_reader__);
			TreeFactory.Register(1826677248,()=>new FireFxAct_StopCameraFollow(),__firefxact_stopcamerafollow_reader__);
			TreeFactory.Register(-1948520311,()=>new FireFxAct_Summon(),__firefxact_summon_reader__);
			TreeFactory.Register(-1725088058,()=>new FireFxAct_Taunt(),__firefxact_taunt_reader__);
			TreeFactory.Register(756458798,()=>new FireFxAct_TempAntiHit(),__firefxact_tempantihit_reader__);
			TreeFactory.Register(-1560785664,()=>new FireFxAct_TimeScale(),__firefxact_timescale_reader__);
			TreeFactory.Register(-1796361558,()=>new FireFxAct_TurnOrient(),__firefxact_turnorient_reader__);
			TreeFactory.Register(1096832719,()=>new FireFxAct_TweenToTarget(),__firefxact_tweentotarget_reader__);
			TreeFactory.Register(-422041356,()=>new FireFxAct_UIEffect(),__firefxact_uieffect_reader__);
			TreeFactory.Register(1218802101,()=>new FireFxAct_UnlockBullet(),__firefxact_unlockbullet_reader__);
			TreeFactory.Register(-860460072,()=>new FireFxAct_WaitHitDone(),__firefxact_waithitdone_reader__);
			TreeFactory.Register(-1078755360,()=>new FireFxAct_HitRebound(),__firefxact_hitrebound_reader__);
			TreeFactory.Register(369978342,()=>new FireFxAct_OnHitHandler(),__firefxact_onhithandler_reader__);
			TreeFactory.Register(-1960653039,()=>new FireFxAct_Shield(),__firefxact_shield_reader__);
			TreeFactory.Register(-418885370,()=>new FireFxAct_SkillProtect(),__firefxact_skillprotect_reader__);
			TreeFactory.Register(2046374672,()=>new SceneActRoot(),null);
			TreeFactory.Register(805753879,()=>new SceneAct_AddPrefab(),__sceneact_addprefab_reader__);
			TreeFactory.Register(-1848620426,()=>new SceneAct_Animation(),__sceneact_animation_reader__);
			TreeFactory.Register(-20029872,()=>new SceneAct_Bubble(),__sceneact_bubble_reader__);
			TreeFactory.Register(-1946709395,()=>new SceneAct_ChangeCpx(),__sceneact_changecpx_reader__);
			TreeFactory.Register(-1376472628,()=>new SceneAct_ChangeSummoner(),__sceneact_changesummoner_reader__);
			TreeFactory.Register(73574745,()=>new SceneAct_DeadAnim(),__sceneact_deadanim_reader__);
			TreeFactory.Register(1073814833,()=>new SceneAct_Dispose(),__sceneact_dispose_reader__);
			TreeFactory.Register(1249621452,()=>new SceneAct_Fight(),__sceneact_fight_reader__);
			TreeFactory.Register(-700743200,()=>new SceneAct_LookAt(),__sceneact_lookat_reader__);
			TreeFactory.Register(-1283687164,()=>new SceneAct_PlayBullet(),__sceneact_playbullet_reader__);
			TreeFactory.Register(-589400620,()=>new SceneAct_PlayFx(),__sceneact_playfx_reader__);
			TreeFactory.Register(521977187,()=>new SceneAct_PlayPlot(),__sceneact_playplot_reader__);
			TreeFactory.Register(729556526,()=>new SceneAct_SetChildActive(),__sceneact_setchildactive_reader__);
			TreeFactory.Register(195019159,()=>new SceneAct_SetFxTarget(),__sceneact_setfxtarget_reader__);
			TreeFactory.Register(1491308747,()=>new SceneAct_SetGameObjectActive(),__sceneact_setgameobjectactive_reader__);
			TreeFactory.Register(937345543,()=>new SceneAct_SetJumpPath(),__sceneact_setjumppath_reader__);
			TreeFactory.Register(1169832825,()=>new SceneAct_SetOcclusion(),__sceneact_setocclusion_reader__);
			TreeFactory.Register(1816107232,()=>new SceneAct_SetWeather(),__sceneact_setweather_reader__);
			TreeFactory.Register(-985965407,()=>new SceneAct_Sound(),__sceneact_sound_reader__);
			TreeFactory.Register(1124529180,()=>new SceneAct_SoundSource(),__sceneact_soundsource_reader__);
			TreeFactory.Register(1210151716,()=>new SceneAct_SoundVolumn(),__sceneact_soundvolumn_reader__);
			TreeFactory.Register(517526549,()=>new SceneAct_SpeakBasic(),__sceneact_speakbasic_reader__);
			TreeFactory.Register(1819235606,()=>new SceneAct_SpeakPanel(),__sceneact_speakpanel_reader__);
			TreeFactory.Register(-1281056301,()=>new SceneAct_StationActivate(),__sceneact_stationactivate_reader__);
			TreeFactory.Register(-2020003333,()=>new SceneAct_StationPanel(),__sceneact_stationpanel_reader__);
			TreeFactory.Register(1874683583,()=>new SceneAct_TeleportActive(),__sceneact_teleportactive_reader__);
			TreeFactory.Register(477222217,()=>new SceneAct_WaitRebirth(),__sceneact_waitrebirth_reader__);
			TreeFactory.Register(-1455979390,()=>new SceneBuff_Speed(),__scenebuff_speed_reader__);
			TreeFactory.Register(1728580721,()=>new SceneComposite_Sequence(),__scenecomposite_sequence_reader__);
			TreeFactory.Register(1384601484,()=>new SceneComposite_SequenceSelector(),__scenecomposite_sequenceselector_reader__);
			TreeFactory.Register(1728577709,()=>new SceneComposite_Parallel(),__scenecomposite_parallel_reader__);
			TreeFactory.Register(-1397043768,()=>new SceneComposite_ParallelSelector(),__scenecomposite_parallelselector_reader__);
			TreeFactory.Register(1734428780,()=>new SceneDeco_Repeat(),__scenedeco_repeat_reader__);
			TreeFactory.Register(-835514951,()=>new SceneDeco_NeverStop(),__scenedeco_neverstop_reader__);
			TreeFactory.Register(1404152921,()=>new SceneDeco_Wait(),__scenedeco_wait_reader__);
			TreeFactory.Register(-969596610,()=>new SceneDeco_UntilSuccess(),__scenedeco_untilsuccess_reader__);
			TreeFactory.Register(787798832,()=>new SceneDeco_UntilFailure(),__scenedeco_untilfailure_reader__);
			TreeFactory.Register(628217511,()=>new SceneDeco_Random(),__scenedeco_random_reader__);
			TreeFactory.Register(1334628911,()=>new SceneAct_CamElacticity(),__sceneact_camelacticity_reader__);
			TreeFactory.Register(-506446496,()=>new SceneAct_CamLock(),__sceneact_camlock_reader__);
			TreeFactory.Register(-2025761309,()=>new SceneAct_CamOrthSize(),__sceneact_camorthsize_reader__);
			TreeFactory.Register(245479107,()=>new SceneAct_CamRelease(),__sceneact_camrelease_reader__);
			TreeFactory.Register(1394971394,()=>new SceneAct_CamTween(),__sceneact_camtween_reader__);
			TreeFactory.Register(702896097,()=>new SceneCon_ActionMode(),__scenecon_actionmode_reader__);
			TreeFactory.Register(651494537,()=>new SceneCon_Career(),__scenecon_career_reader__);
			TreeFactory.Register(772233861,()=>new SceneCon_DistanceCompare(),__scenecon_distancecompare_reader__);
			TreeFactory.Register(769585100,()=>new SceneCon_Gender(),__scenecon_gender_reader__);
			TreeFactory.Register(856671517,()=>new SceneCon_HasMission(),__scenecon_hasmission_reader__);
			TreeFactory.Register(-842920867,()=>new SceneCon_HasScene(),__scenecon_hasscene_reader__);
			TreeFactory.Register(520400101,()=>new SceneCon_HasStation(),__scenecon_hasstation_reader__);
			TreeFactory.Register(-841844545,()=>new SceneCon_HasThing(),__scenecon_hasthing_reader__);
			TreeFactory.Register(-1943301179,()=>new SceneCon_Hero(),__scenecon_hero_reader__);
			TreeFactory.Register(-129305553,()=>new SceneCon_NavLayerCheck(),__scenecon_navlayercheck_reader__);
			TreeFactory.Register(-1904722108,()=>new SceneCon_NeareatObj(),__scenecon_neareatobj_reader__);
			TreeFactory.Register(-2042214410,()=>new SceneCon_NpcEnterIdleCount(),__scenecon_npcenteridlecount_reader__);
			TreeFactory.Register(-1965308896,()=>new SceneCon_Property(),__scenecon_property_reader__);
			TreeFactory.Register(1530041595,()=>new SceneAct_CollidePlant(),__sceneact_collideplant_reader__);
			TreeFactory.Register(-1068714081,()=>new SceneAct_Dissovle(),__sceneact_dissovle_reader__);
			TreeFactory.Register(-1370857426,()=>new SceneAct_GroundColor(),__sceneact_groundcolor_reader__);
			TreeFactory.Register(-1887109299,()=>new SceneAct_SetFootEffect(),__sceneact_setfooteffect_reader__);
			TreeFactory.Register(256292241,()=>new SceneAct_SetFootPrint(),__sceneact_setfootprint_reader__);
			TreeFactory.Register(1328327180,()=>new SceneAct_SetScratch(),__sceneact_setscratch_reader__);
			TreeFactory.Register(1470843572,()=>new SceneAct_SetShadow(),__sceneact_setshadow_reader__);
			TreeFactory.Register(-1061069629,()=>new SceneFx_ColorMatrix(),__scenefx_colormatrix_reader__);
			TreeFactory.Register(813652171,()=>new SceneFx_DisperTile(),__scenefx_dispertile_reader__);
			TreeFactory.Register(1664162802,()=>new SceneAct_CurveMove(),__sceneact_curvemove_reader__);
			TreeFactory.Register(-1676877694,()=>new SceneAct_GotoTarget(),__sceneact_gototarget_reader__);
			TreeFactory.Register(2046142044,()=>new SceneAct_Jump(),__sceneact_jump_reader__);
			TreeFactory.Register(1443177947,()=>new SceneAct_LinearChase(),__sceneact_linearchase_reader__);
			TreeFactory.Register(-1760514327,()=>new SceneAct_Path(),__sceneact_path_reader__);
			TreeFactory.Register(319021549,()=>new SceneAct_Position(),__sceneact_position_reader__);
			TreeFactory.Register(-1452205210,()=>new SceneTween_Color(),__scenetween_color_reader__);
			TreeFactory.Register(-1810941073,()=>new SceneTween_LookAt(),__scenetween_lookat_reader__);
			TreeFactory.Register(-1722467166,()=>new SceneTween_Orient(),__scenetween_orient_reader__);
			TreeFactory.Register(1061907394,()=>new SceneTween_Path(),__scenetween_path_reader__);
			TreeFactory.Register(1936886342,()=>new SceneTween_Position(),__scenetween_position_reader__);
			TreeFactory.Register(-1437797043,()=>new SceneTween_Scale(),__scenetween_scale_reader__);
			TreeFactory.Register(-740351023,()=>new SceneTween_Translate(),__scenetween_translate_reader__);
			TreeFactory.Register(917720936,()=>new SceneTween_TweenChildPosition(),__scenetween_tweenchildposition_reader__);
			TreeFactory.Register(260757845,()=>new SceneAct_LockIO(),__sceneact_lockio_reader__);
			TreeFactory.Register(1753624019,()=>new SceneAct_WaitButton(),__sceneact_waitbutton_reader__);
			TreeFactory.Register(-1026035391,()=>new SceneNpc_Adhere(),__scenenpc_adhere_reader__);
			TreeFactory.Register(-1473126153,()=>new SceneNpc_CallMakeInteractiveDrop(),__scenenpc_callmakeinteractivedrop_reader__);
			TreeFactory.Register(931290905,()=>new SceneNpc_CallNpcFunc(),__scenenpc_callnpcfunc_reader__);
			TreeFactory.Register(1125722000,()=>new SceneNpc_CenterWander(),__scenenpc_centerwander_reader__);
			TreeFactory.Register(-1725793425,()=>new SceneNpc_ChangeActionSource(),__scenenpc_changeactionsource_reader__);
			TreeFactory.Register(-218152671,()=>new SceneNpc_ChangeMode(),__scenenpc_changemode_reader__);
			TreeFactory.Register(-179039622,()=>new SceneNpc_ChangeRelatioin(),__scenenpc_changerelatioin_reader__);
			TreeFactory.Register(1833463092,()=>new SceneNpc_ChangeThink(),__scenenpc_changethink_reader__);
			TreeFactory.Register(-1328840819,()=>new SceneNpc_ClearActionCache(),__scenenpc_clearactioncache_reader__);
			TreeFactory.Register(-1021672209,()=>new SceneNpc_FightWander(),__scenenpc_fightwander_reader__);
			TreeFactory.Register(-783944549,()=>new SceneNpc_IsMode(),__scenenpc_ismode_reader__);
			TreeFactory.Register(-286846851,()=>new SceneNpc_MoveToBirthPlace(),__scenenpc_movetobirthplace_reader__);
			TreeFactory.Register(932349124,()=>new SceneNpc_OpenDropBox(),__scenenpc_opendropbox_reader__);
			TreeFactory.Register(-390615926,()=>new SceneNpc_OpenFuncPanel(),__scenenpc_openfuncpanel_reader__);
			TreeFactory.Register(-37152729,()=>new SceneNpc_OpenPanel(),__scenenpc_openpanel_reader__);
			TreeFactory.Register(517497932,()=>new SceneNpc_PatrolArea(),__scenenpc_patrolarea_reader__);
			TreeFactory.Register(517928932,()=>new SceneNpc_PatrolPath(),__scenenpc_patrolpath_reader__);
			TreeFactory.Register(674796596,()=>new SceneNpc_PatrolRange(),__scenenpc_patrolrange_reader__);
			TreeFactory.Register(1723378293,()=>new SceneNpc_PeronalityProperty(),__scenenpc_peronalityproperty_reader__);
			TreeFactory.Register(682982172,()=>new SceneNpc_SetBirthPlace(),__scenenpc_setbirthplace_reader__);
			TreeFactory.Register(93051342,()=>new SceneNpc_SetInteractive(),__scenenpc_setinteractive_reader__);
			TreeFactory.Register(-1129649312,()=>new SceneNpc_SetNearByArea(),__scenenpc_setnearbyarea_reader__);
			TreeFactory.Register(1463182231,()=>new SceneNpc_SetNearByPrefab(),__scenenpc_setnearbyprefab_reader__);
			TreeFactory.Register(-1135037740,()=>new SceneNpcCon_MagicBox(),__scenenpccon_magicbox_reader__);
			TreeFactory.Register(-1651090376,()=>new SceneNpc_UseSkill(),__scenenpc_useskill_reader__);
			TreeFactory.Register(-414730665,()=>new SceneNpc_WaitPanelClose(),__scenenpc_waitpanelclose_reader__);
			TreeFactory.Register(-323984160,()=>new SceneAct_CollideSetting(),__sceneact_collidesetting_reader__);
			TreeFactory.Register(-742143890,()=>new SceneAct_CountOnNavLayer(),__sceneact_countonnavlayer_reader__);
			TreeFactory.Register(-1830456165,()=>new SceneAct_OnNavLayer(),__sceneact_onnavlayer_reader__);
			TreeFactory.Register(-1026607486,()=>new SceneAct_SetNavLayer(),__sceneact_setnavlayer_reader__);
			TreeFactory.Register(-509447785,()=>new SceneAct_SetRvo(),__sceneact_setrvo_reader__);
			TreeFactory.Register(-1615688666,()=>new ScenePlot_Actor(),__sceneplot_actor_reader__);
			TreeFactory.Register(-2084944194,()=>new ScenePlot_FilmScreen(),__sceneplot_filmscreen_reader__);
			TreeFactory.Register(-1601901515,()=>new ScenePlot_Panel(),__sceneplot_panel_reader__);
			TreeFactory.Register(904374916,()=>new ScenePlot_PanelContent(),__sceneplot_panelcontent_reader__);
			TreeFactory.Register(731796565,()=>new ScenePlot_TargetActor(),__sceneplot_targetactor_reader__);
			TreeFactory.Register(-786020717,()=>new ScenePlot_TargetActorGroup(),__sceneplot_targetactorgroup_reader__);
			TreeFactory.Register(2112035236,()=>new SceneAct_ActivateMission(),__sceneact_activatemission_reader__);
			TreeFactory.Register(-1465461911,()=>new SceneAct_CallBuildingBuff(),__sceneact_callbuildingbuff_reader__);
			TreeFactory.Register(1483484605,()=>new SceneAct_CallGather(),__sceneact_callgather_reader__);
			TreeFactory.Register(1374355994,()=>new SceneAct_CallPathFinish(),__sceneact_callpathfinish_reader__);
			TreeFactory.Register(-1265963199,()=>new SceneAct_EnterArea(),__sceneact_enterarea_reader__);
			TreeFactory.Register(1396448700,()=>new SceneAct_RandomTeleport(),__sceneact_randomteleport_reader__);
			TreeFactory.Register(1460035699,()=>new SceneAct_UIEffect(),__sceneact_uieffect_reader__);
			TreeFactory.Register(-1923396511,()=>new SceneAct_UIShowHide(),__sceneact_uishowhide_reader__);
			TreeFactory.Register(-459799065,()=>new PersonalityRoot(),null);
			TreeFactory.Register(677905082,()=>new PersonalityComposition_Seq(),__personalitycomposition_seq_reader__);
			TreeFactory.Register(-925858192,()=>new PersonalityComposition_Pal(),__personalitycomposition_pal_reader__);
			TreeFactory.Register(2095345252,()=>new PersonalityComposition_Selector(),__personalitycomposition_selector_reader__);
			TreeFactory.Register(501759112,()=>new PersonalityDeco_Random(),__personalitydeco_random_reader__);
			TreeFactory.Register(-581754573,()=>new Personality_IterAlarmMessage(),__personality_iteralarmmessage_reader__);
			TreeFactory.Register(855412610,()=>new Personality_IterAttackMessage(),__personality_iterattackmessage_reader__);
			TreeFactory.Register(-220352215,()=>new Personality_IterHelpMessage(),__personality_iterhelpmessage_reader__);
			TreeFactory.Register(-303774838,()=>new Personality_IterTargetInSight(),__personality_itertargetinsight_reader__);
			TreeFactory.Register(-1371707185,()=>new Personality_OnHit(),__personality_onhit_reader__);
			TreeFactory.Register(426904551,()=>new Personality_OnMode(),__personality_onmode_reader__);
			TreeFactory.Register(-89368486,()=>new Personality_Property(),__personality_property_reader__);
			TreeFactory.Register(32785933,()=>new Personality_Attack(),__personality_attack_reader__);
			TreeFactory.Register(730782020,()=>new Personality_CallHelp(),__personality_callhelp_reader__);
			TreeFactory.Register(-1854850463,()=>new Personality_CallPolice(),__personality_callpolice_reader__);
			TreeFactory.Register(-460159765,()=>new Personality_Flee(),__personality_flee_reader__);
			TreeFactory.Register(198636558,()=>new Personality_GoHelp(),__personality_gohelp_reader__);
			TreeFactory.Register(-1188545222,()=>new Personality_Investigate(),__personality_investigate_reader__);
			TreeFactory.Register(-10492972,()=>new DialogRoot(),null);
			TreeFactory.Register(-1946184284,()=>new Dialog_Button(),__dialog_button_reader__);
			TreeFactory.Register(508007495,()=>new Dialog_Content(),__dialog_content_reader__);
			TreeFactory.Register(-335351347,()=>new Dialog_Group(),__dialog_group_reader__);
			TreeFactory.Register(1890902816,()=>new PanelSetting_ChildNormal(),__panelsetting_childnormal_reader__);
			TreeFactory.Register(1849232895,()=>new PanelSetting_ChildManual(),__panelsetting_childmanual_reader__);
			TreeFactory.Register(2024626997,()=>new PanelSetting_ChildSelect(),__panelsetting_childselect_reader__);
			TreeFactory.Register(-1596821201,()=>new PanelSetting_ChildStack(),__panelsetting_childstack_reader__);
			TreeFactory.Register(-630347106,()=>new PanelSetting_AutoClose(),__panelsetting_autoclose_reader__);
			TreeFactory.Register(1828613706,()=>new PanelSetting_Duration(),__panelsetting_duration_reader__);
			TreeFactory.Register(-1483249278,()=>new PanelSetting_Lua(),__panelsetting_lua_reader__);
			TreeFactory.Register(-1535185404,()=>new PanelSetting_OpenToObject(),__panelsetting_opentoobject_reader__);
			TreeFactory.Register(1263644968,()=>new PanelSetting_Cull(),__panelsetting_cull_reader__);
			TreeFactory.Register(-460501386,()=>new PanelSetting_TweenScreen(),__panelsetting_tweenscreen_reader__);
			TreeFactory.Register(-1123250243,()=>new PanelSetting_AnimationOnly(),__panelsetting_animationonly_reader__);
			TreeFactory.Register(-832406366,()=>new PanelSetting_Module(),__panelsetting_module_reader__);
			TreeFactory.Register(1263629592,()=>new PanelSetting_Panel(),__panelsetting_panel_reader__);
			TreeFactory.Register(1264086168,()=>new PanelSetting_Root(),null);
		}
			private static void __sceneact_hudshowhide_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_HudShowHide obj=(SceneAct_HudShowHide)tree;
				obj.enabled=ba.ReadBool();
				obj.show=ba.ReadBool();
			}
			private static void __sceneact_playoffsetbullet_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_PlayOffsetBullet obj=(SceneAct_PlayOffsetBullet)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.interval=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.distance=ba.ReadFloat();
				obj.offset=ByteArrayTools.ReadVec2(ba);
			}
			private static void __sceneact_playskeletonanimation_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_PlaySkeletonAnimation obj=(SceneAct_PlaySkeletonAnimation)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.loopTime=ba.ReadInt();
				obj.animName=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __scenecon_fieldnumber_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_FieldNumber obj=(SceneCon_FieldNumber)tree;
				obj.enabled=ba.ReadBool();
				obj.fieldId=ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.maxValue=ba.ReadInt();
			}
			private static void __scenenpc_closepanel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ClosePanel obj=(SceneNpc_ClosePanel)tree;
				obj.enabled=ba.ReadBool();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __scenenpc_removeshadow_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_RemoveShadow obj=(SceneNpc_RemoveShadow)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __sceneplot_announce_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_Announce obj=(ScenePlot_Announce)tree;
				obj.enabled=ba.ReadBool();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.langTxt=ByteArrayTools.ReadString(ba,sp);
				obj.time=ba.ReadFloat();
			}
			private static void __firefxserver_notifycollect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxServer_NotifyCollect obj=(FireFxServer_NotifyCollect)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxserver_notifypicker_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxServer_NotifyPicker obj=(FireFxServer_NotifyPicker)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_controlmove_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ControlMove obj=(FireFxAct_ControlMove)tree;
				obj.enabled=ba.ReadBool();
				obj.forcast=ba.ReadFloat();
				obj.speed=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
				obj.stopOnRelease=ba.ReadBool();
				obj.updateOrient=ba.ReadBool();
			}
			private static void __firefxact_controlorient_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ControlOrient obj=(FireFxAct_ControlOrient)tree;
				obj.enabled=ba.ReadBool();
				obj.notifier=ByteArrayTools.ReadString(ba,sp);
				obj.notifyInterval=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_holdkey_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_HoldKey obj=(FireFxAct_HoldKey)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_iomovewatcher_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_IOMoveWatcher obj=(FireFxAct_IOMoveWatcher)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_presskey_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_PressKey obj=(FireFxAct_PressKey)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_setcombo_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_SetCombo obj=(FireFxAct_SetCombo)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
				obj.combos=ByteArrayTools.ReadArray<ComboVO>(ba,sp);
			}
			private static void __firefxhit_addbuff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_AddBuff obj=(FireFxHit_AddBuff)tree;
				obj.enabled=ba.ReadBool();
				obj.buff=ba.ReadInt();
				obj.target=(Relation)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.maxOverlay=ba.ReadInt();
			}
			private static void __firefxhit_addhitpower_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_AddHitPower obj=(FireFxHit_AddHitPower)tree;
				obj.enabled=ba.ReadBool();
				obj.power=ba.ReadInt();
			}
			private static void __firefxhit_break_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_Break obj=(FireFxHit_Break)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.scaleType=(TimeScaleType)ba.ReadInt();
				obj.scale=ba.ReadFloat();
			}
			private static void __firefxhit_buff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_Buff obj=(FireFxHit_Buff)tree;
				obj.enabled=ba.ReadBool();
				obj.buff=ba.ReadInt();
				obj.target=(Relation)ba.ReadInt();
			}
			private static void __firefxhit_checkhittarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_CheckHitTarget obj=(FireFxHit_CheckHitTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.check=ba.ReadBool();
			}
			private static void __firefxhit_damage_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_Damage obj=(FireFxHit_Damage)tree;
				obj.enabled=ba.ReadBool();
				obj.moveIndex=ba.ReadInt();
				obj.buff=ba.ReadInt();
				obj.scope=ba.ReadInt();
				obj.ceoffMode=ba.ReadInt();
				obj.elementMode=ba.ReadInt();
				obj.rangeMode=ba.ReadInt();
				obj.weaponMode=ba.ReadInt();
			}
			private static void __firefxhit_end_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_End obj=(FireFxHit_End)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxhit_hitaway_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_HitAway obj=(FireFxHit_HitAway)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.distance=ba.ReadFloat();
				obj.maxAngles=ba.ReadInt();
				obj.powerSource=(BuffPropertySource)ba.ReadInt();
				obj.power=ba.ReadInt();
				obj.collideBuffId=ba.ReadInt();
				obj.stun=ba.ReadFloat();
				obj.distanceSource=(BuffPropertySource)ba.ReadInt();
			}
			private static void __firefxhit_hitdnf_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_HitDnf obj=(FireFxHit_HitDnf)tree;
				obj.enabled=ba.ReadBool();
				obj.ground=ByteArrayTools.Read<PunchPower>(ba,sp);
				obj.air=ByteArrayTools.Read<PunchPower>(ba,sp);
				obj.powerSource=(BuffPropertySource)ba.ReadInt();
				obj.fly=ba.ReadBool();
				obj.ignoreAnimInterval=ba.ReadFloat();
				obj.collideBuffId=ba.ReadInt();
				obj.maxAngles=ba.ReadInt();
			}
			private static void __firefxhit_hitpull_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_HitPull obj=(FireFxHit_HitPull)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.ease=(EaseType)ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.powerSource=(BuffPropertySource)ba.ReadInt();
				obj.power=ba.ReadInt();
				obj.collideBuffId=ba.ReadInt();
				obj.stun=ba.ReadFloat();
			}
			private static void __firefxhit_hitpush_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_HitPush obj=(FireFxHit_HitPush)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.ease=(EaseType)ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.powerSource=(BuffPropertySource)ba.ReadInt();
				obj.power=ba.ReadInt();
				obj.collideBuffId=ba.ReadInt();
				obj.stun=ba.ReadFloat();
				obj.distanceSource=(BuffPropertySource)ba.ReadInt();
			}
			private static void __firefxhit_itertargets_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_IterTargets obj=(FireFxHit_IterTargets)tree;
				obj.enabled=ba.ReadBool();
				obj.hitFly=ba.ReadBool();
				obj.hitPower=ba.ReadFloat();
				obj.stun=ba.ReadFloat();
				obj.flyStun=ba.ReadFloat();
				obj.mode=(FireFxHit_IterTargets.Mode)ba.ReadInt();
			}
			private static void __firefxhit_runtimeproperty_add_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_RuntimeProperty_Add obj=(FireFxHit_RuntimeProperty_Add)tree;
				obj.enabled=ba.ReadBool();
				obj.property=ba.ReadInt();
				obj.target=(SkillTargetMask)ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.isPercent=ba.ReadBool();
			}
			private static void __firefxhit_runtimeproperty_set_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_RuntimeProperty_Set obj=(FireFxHit_RuntimeProperty_Set)tree;
				obj.enabled=ba.ReadBool();
				obj.property=ba.ReadInt();
				obj.target=(SkillTargetMask)ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.isPercent=ba.ReadBool();
			}
			private static void __firefxhit_screentargetrotate_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_ScreenTargetRotate obj=(FireFxHit_ScreenTargetRotate)tree;
				obj.enabled=ba.ReadBool();
				obj.rangeSource=(BuffPropertySource)ba.ReadInt();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(Relation)ba.ReadInt();
				obj.isExecute=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.start=ba.ReadFloat();
				obj.angle=ba.ReadFloat();
				obj.radius=ba.ReadFloat();
				obj.stopAsHit=ba.ReadBool();
			}
			private static void __firefxhit_setflytext_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_SetFlyText obj=(FireFxHit_SetFlyText)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __firefxhit_sethitphase_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_SetHitPhase obj=(FireFxHit_SetHitPhase)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(SkillHitPhase)ba.ReadInt();
			}
			private static void __firefxhit_setskillceoff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_SetSkillCeoff obj=(FireFxHit_SetSkillCeoff)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(SkillCeoffMode)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __firefxhit_triggerpropbuff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_TriggerPropBuff obj=(FireFxHit_TriggerPropBuff)tree;
				obj.enabled=ba.ReadBool();
				obj.pos=ba.ReadLong();
			}
			private static void __firefxhit_callbuff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_CallBuff obj=(FireFxHit_CallBuff)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(BuffPhase)ba.ReadInt();
			}
			private static void __firefxhit_callfightevent_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxHit_CallFightEvent obj=(FireFxHit_CallFightEvent)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=ba.ReadInt();
			}
			private static void __firefxcomposite_sequence_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxComposite_Sequence obj=(FirefxComposite_Sequence)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __firefxcomposite_sequenceselector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxComposite_SequenceSelector obj=(FirefxComposite_SequenceSelector)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __firefxcomposite_parallel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxComposite_Parallel obj=(FirefxComposite_Parallel)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __firefxcomposite_parallelselector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxComposite_ParallelSelector obj=(FirefxComposite_ParallelSelector)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __firefxdeco_time_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_Time obj=(FirefxDeco_Time)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationRange=ByteArrayTools.ReadVec2(ba);
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __firefxdeco_random_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_Random obj=(FirefxDeco_Random)tree;
				obj.enabled=ba.ReadBool();
				obj.method=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __firefxdeco_repeat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_Repeat obj=(FirefxDeco_Repeat)tree;
				obj.enabled=ba.ReadBool();
				obj.count=ba.ReadInt();
				obj.min=ba.ReadInt();
				obj.max=ba.ReadInt();
			}
			private static void __firefxdeco_untilsuccess_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_UntilSuccess obj=(FirefxDeco_UntilSuccess)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxdeco_untilfailure_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_UntilFailure obj=(FirefxDeco_UntilFailure)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxdeco_neverstop_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FirefxDeco_NeverStop obj=(FirefxDeco_NeverStop)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxcon_actionindex_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_ActionIndex obj=(FireFxCon_ActionIndex)tree;
				obj.enabled=ba.ReadBool();
				obj.index=ba.ReadInt();
			}
			private static void __firefxcon_checkcombotime_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_CheckComboTime obj=(FireFxCon_CheckComboTime)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.key=ba.ReadInt();
			}
			private static void __firefxcon_checkhitindex_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_CheckHitIndex obj=(FireFxCon_CheckHitIndex)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadInt();
			}
			private static void __firefxcon_checkhitqty_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_CheckHitQty obj=(FireFxCon_CheckHitQty)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadInt();
				obj.mode=(HitCountMode)ba.ReadInt();
			}
			private static void __firefxcon_checkmonstertype_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_CheckMonsterType obj=(FireFxCon_CheckMonsterType)tree;
				obj.enabled=ba.ReadBool();
				obj.target=(SkillTarget)ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.monterType=(MonsterType)ba.ReadInt();
			}
			private static void __firefxcon_countsurround_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_CountSurround obj=(FireFxCon_CountSurround)tree;
				obj.enabled=ba.ReadBool();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.relation=(Relation)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadInt();
			}
			private static void __firefxcon_holdkeytime_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_HoldKeyTime obj=(FireFxCon_HoldKeyTime)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxcon_iscritical_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_IsCritical obj=(FireFxCon_IsCritical)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=ba.ReadBool();
			}
			private static void __firefxcon_isdoublehit_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_IsDoubleHit obj=(FireFxCon_IsDoubleHit)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=ba.ReadBool();
			}
			private static void __firefxcon_ishost_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_IsHost obj=(FireFxCon_IsHost)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=ba.ReadBool();
			}
			private static void __firefxcon_iskillhit_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_IsKillHit obj=(FireFxCon_IsKillHit)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=ba.ReadBool();
			}
			private static void __firefxcon_onhit_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_OnHit obj=(FireFxCon_OnHit)tree;
				obj.enabled=ba.ReadBool();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxcon_removetarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_RemoveTarget obj=(FireFxCon_RemoveTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.rangeSource=(BuffPropertySource)ba.ReadInt();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(Relation)ba.ReadInt();
			}
			private static void __firefxcon_searchsummon_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_SearchSummon obj=(FireFxCon_SearchSummon)tree;
				obj.enabled=ba.ReadBool();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
			}
			private static void __firefxcon_searchtarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_SearchTarget obj=(FireFxCon_SearchTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.rangeSource=(BuffPropertySource)ba.ReadInt();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(Relation)ba.ReadInt();
				obj.isExecute=ba.ReadBool();
			}
			private static void __firefxcon_timetoprevfiretime_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxCon_TimeToPrevFireTime obj=(FireFxCon_TimeToPrevFireTime)tree;
				obj.enabled=ba.ReadBool();
				obj.method=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __bulletact_animationmove_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_AnimationMove obj=(BulletAct_AnimationMove)tree;
				obj.enabled=ba.ReadBool();
				obj.asset=ByteArrayTools.ReadString(ba,sp);
				obj.orientMode=(OrientMode)ba.ReadInt();
			}
			private static void __bulletact_bindbone_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_BindBone obj=(BulletAct_BindBone)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.boneTarget=(BoneTarget)ba.ReadInt();
				obj.boneName=ByteArrayTools.ReadString(ba,sp);
				obj.offset=ByteArrayTools.ReadVec3(ba);
				obj.rotate=ByteArrayTools.ReadVec3(ba);
				obj.scale=ByteArrayTools.ReadVec3(ba);
			}
			private static void __bulletact_bounce_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Bounce obj=(BulletAct_Bounce)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __bulletact_buffarea_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_BuffArea obj=(BulletAct_BuffArea)tree;
				obj.enabled=ba.ReadBool();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.buff=ba.ReadInt();
				obj.buffDuration=ba.ReadFloat();
				obj.buffMaxOverlay=ba.ReadInt();
				obj.target=(Relation)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.timeScale=(TimeScaleType)ba.ReadInt();
			}
			private static void __bulletact_chain_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Chain obj=(BulletAct_Chain)tree;
				obj.enabled=ba.ReadBool();
				obj.asset=ByteArrayTools.ReadString(ba,sp);
				obj.mode=(BulletChainMode)ba.ReadInt();
				obj.size=ba.ReadFloat();
			}
			private static void __bulletact_collide_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Collide obj=(BulletAct_Collide)tree;
				obj.enabled=ba.ReadBool();
				obj.radius=ba.ReadFloat();
				obj.mode=(Relation)ba.ReadInt();
				obj.maxCount=ba.ReadInt();
			}
			private static void __bulletact_create_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Create obj=(BulletAct_Create)tree;
				obj.enabled=ba.ReadBool();
				obj.asset=ByteArrayTools.ReadString(ba,sp);
				obj.mirror=ba.ReadBool();
				obj.mirrorSector=ba.ReadFloat();
				obj.faceCamera=ba.ReadBool();
				obj.supportAngle=ba.ReadInt();
				obj.angleOffset=ba.ReadFloat();
			}
			private static void __bulletact_createtrap_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_CreateTrap obj=(BulletAct_CreateTrap)tree;
				obj.enabled=ba.ReadBool();
				obj.skillId=ba.ReadInt();
			}
			private static void __bulletact_disposewithrunner_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_DisposeWithRunner obj=(BulletAct_DisposeWithRunner)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __bulletact_dissolve_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Dissolve obj=(BulletAct_Dissolve)tree;
				obj.enabled=ba.ReadBool();
				obj.show=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __bulletact_fetchtarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_FetchTarget obj=(BulletAct_FetchTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.targetType=(TargetType)ba.ReadInt();
			}
			private static void __bulletact_lookat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_LookAt obj=(BulletAct_LookAt)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.dirMode=(BulletDirMode)ba.ReadInt();
				obj.hostType=(BulletLookAtHost)ba.ReadInt();
				obj.updatePosition=ba.ReadBool();
			}
			private static void __bulletact_play_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Play obj=(BulletAct_Play)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.dirMode=(BulletDirMode)ba.ReadInt();
				obj.disposeWithRunner=ba.ReadBool();
			}
			private static void __bulletact_startlighten_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_StartLighten obj=(BulletAct_StartLighten)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __bulletact_tween_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Tween obj=(BulletAct_Tween)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.loopType=(TweenWrapMode)ba.ReadInt();
				obj.loops=ba.ReadInt();
			}
			private static void __bulletact_twirl_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Twirl obj=(BulletAct_Twirl)tree;
				obj.enabled=ba.ReadBool();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.central=(BulletLookAtHost)ba.ReadInt();
				obj.rotateSpeed=ByteArrayTools.ReadVec2(ba);
				obj.fowardSpeed=ByteArrayTools.ReadVec2(ba);
				obj.duration=ByteArrayTools.ReadVec2(ba);
				obj.selfDuration=ByteArrayTools.ReadVec2(ba);
				obj.euler=ByteArrayTools.ReadVec3(ba);
			}
			private static void __bulletcon_life_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCon_Life obj=(BulletCon_Life)tree;
				obj.enabled=ba.ReadBool();
				obj.length=ba.ReadFloat();
				obj.compare=(ValueCompareType)ba.ReadInt();
			}
			private static void __bulletcon_screenseektarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCon_ScreenSeekTarget obj=(BulletCon_ScreenSeekTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.relation=(Relation)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
			}
			private static void __bulletcon_searchtarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCon_SearchTarget obj=(BulletCon_SearchTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(Relation)ba.ReadInt();
				obj.isExecute=ba.ReadBool();
				obj.once=ba.ReadBool();
				obj.interval=ba.ReadFloat();
			}
			private static void __bulletact_fly_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Fly obj=(BulletAct_Fly)tree;
				obj.enabled=ba.ReadBool();
				obj.speed=ba.ReadFloat();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.direction=(BulletFlyDirection)ba.ReadInt();
				obj.orientDir=(BulletFlyDirection)ba.ReadInt();
				obj.maxDistance=ba.ReadFloat();
				obj.useMaxDistance=ba.ReadBool();
				obj.markAsFlyBullet=ba.ReadBool();
				obj.useCollide=ba.ReadBool();
			}
			private static void __bulletact_flysin_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_FlySin obj=(BulletAct_FlySin)tree;
				obj.enabled=ba.ReadBool();
				obj.speed=ba.ReadFloat();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.direction=(BulletFlyDirection)ba.ReadInt();
				obj.orientDir=(BulletFlyDirection)ba.ReadInt();
				obj.maxDistance=ba.ReadFloat();
				obj.useMaxDistance=ba.ReadBool();
				obj.markAsFlyBullet=ba.ReadBool();
				obj.angleStep=ByteArrayTools.ReadVec2(ba);
				obj.curveLength=ByteArrayTools.ReadVec2(ba);
			}
			private static void __bulletact_follow_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Follow obj=(BulletAct_Follow)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.markAsFlyBullet=ba.ReadBool();
			}
			private static void __bulletact_parabola_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Parabola obj=(BulletAct_Parabola)tree;
				obj.enabled=ba.ReadBool();
				obj.velocity=ByteArrayTools.ReadVec3(ba);
				obj.gravity=ByteArrayTools.ReadVec3(ba);
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
				obj.maxDistance=ba.ReadFloat();
				obj.markAsFlyBullet=ba.ReadBool();
			}
			private static void __bulletact_seek_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_Seek obj=(BulletAct_Seek)tree;
				obj.enabled=ba.ReadBool();
				obj.velocityOffset=ByteArrayTools.ReadVec3(ba);
				obj.velocityMax=ByteArrayTools.ReadVec3(ba);
				obj.velocityMin=ByteArrayTools.ReadVec3(ba);
				obj.speed=ba.ReadFloat();
				obj.maxSpeed=ba.ReadFloat();
				obj.maxForce=ba.ReadFloat();
				obj.arriveThreshhold=ba.ReadFloat();
				obj.mass=ba.ReadFloat();
				obj.maxDuration=ba.ReadFloat();
				obj.seekTarget=(SeekTarget)ba.ReadInt();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
				obj.markAsFlyBullet=ba.ReadBool();
				obj.shortestPath=ba.ReadBool();
			}
			private static void __bulletemitter_bounce_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletEmitter_Bounce obj=(BulletEmitter_Bounce)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.delay=ba.ReadFloat();
				obj.delayScaleType=(TimeScaleType)ba.ReadInt();
				obj.numParticle=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.useRandomDelay=ba.ReadBool();
				obj.startMode=(BulletPositionMode)ba.ReadInt();
				obj.destinyMode=(BulletPositionMode)ba.ReadInt();
				obj.rangeSource=(BuffPropertySource)ba.ReadInt();
				obj.bounceRange=ba.ReadFloat();
				obj.countSource=(BuffPropertySource)ba.ReadInt();
				obj.maxBounce=ba.ReadInt();
				obj.searchRelation=(Relation)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
			}
			private static void __bulletemitter_line_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletEmitter_Line obj=(BulletEmitter_Line)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.delay=ba.ReadFloat();
				obj.delayScaleType=(TimeScaleType)ba.ReadInt();
				obj.numParticle=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.useRandomDelay=ba.ReadBool();
				obj.maxDistance=ba.ReadFloat();
				obj.minDistance=ba.ReadFloat();
				obj.startMode=(BulletPositionMode)ba.ReadInt();
				obj.destinyMode=(BulletPositionMode)ba.ReadInt();
				obj.lineMode=(EmitQtyMode)ba.ReadInt();
				obj.space=ba.ReadFloat();
				obj.dirMode=(EmitDir)ba.ReadInt();
			}
			private static void __bulletemitter_normal_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletEmitter_Normal obj=(BulletEmitter_Normal)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.delay=ba.ReadFloat();
				obj.delayScaleType=(TimeScaleType)ba.ReadInt();
				obj.numParticle=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.useRandomDelay=ba.ReadBool();
				obj.maxDistance=ba.ReadFloat();
				obj.minDistance=ba.ReadFloat();
				obj.startMode=(BulletPositionMode)ba.ReadInt();
				obj.destinyMode=(BulletPositionMode)ba.ReadInt();
			}
			private static void __bulletact_captureround_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletAct_CaptureRound obj=(BulletAct_CaptureRound)tree;
				obj.enabled=ba.ReadBool();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(Relation)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __bulletcapture_addbuff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCapture_AddBuff obj=(BulletCapture_AddBuff)tree;
				obj.enabled=ba.ReadBool();
				obj.buffId=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.overlap=ba.ReadInt();
			}
			private static void __bulletcapture_lockslot_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCapture_LockSlot obj=(BulletCapture_LockSlot)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
			}
			private static void __bulletcapture_speedslow_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletCapture_SpeedSlow obj=(BulletCapture_SpeedSlow)tree;
				obj.enabled=ba.ReadBool();
				obj.multiple=ba.ReadFloat();
			}
			private static void __bulletposition_area_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_Area obj=(BulletPosition_Area)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
				obj.minArea=ByteArrayTools.ReadVec3(ba);
				obj.maxArea=ByteArrayTools.ReadVec3(ba);
				obj.offset=ByteArrayTools.ReadVec3(ba);
				obj.bindScreen=ba.ReadBool();
			}
			private static void __bulletposition_dividspace_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_DividSpace obj=(BulletPosition_DividSpace)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
			}
			private static void __bulletposition_hostposition_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_HostPosition obj=(BulletPosition_HostPosition)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
				obj.offset=ByteArrayTools.ReadVec3(ba);
			}
			private static void __bulletposition_line_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_Line obj=(BulletPosition_Line)tree;
				obj.enabled=ba.ReadBool();
				obj.spaceMode=(BulletSpaceMode)ba.ReadInt();
			}
			private static void __bulletposition_resetdestiny_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_ResetDestiny obj=(BulletPosition_ResetDestiny)tree;
				obj.enabled=ba.ReadBool();
				obj.distance=ba.ReadFloat();
				obj.method=(BulletDestinyResetMethod)ba.ReadInt();
			}
			private static void __bulletposition_ring_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_Ring obj=(BulletPosition_Ring)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
				obj.radiusMode=(BulletRingRadiusMode)ba.ReadInt();
				obj.angleMode=(BulletRingAngleMode)ba.ReadInt();
				obj.maxRadius=ba.ReadFloat();
				obj.minRadius=ba.ReadFloat();
				obj.numRings=ba.ReadInt();
				obj.numBulletPerRing=ba.ReadInt();
			}
			private static void __bulletposition_screen_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_Screen obj=(BulletPosition_Screen)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
				obj.screen=ByteArrayTools.ReadVec2(ba);
			}
			private static void __bulletposition_searchinsight_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_SearchInSight obj=(BulletPosition_SearchInSight)tree;
				obj.enabled=ba.ReadBool();
				obj.calcMode=(BulletCalcMode)ba.ReadInt();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
				obj.relation=(Relation)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
			}
			private static void __bulletposition_sector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				BulletPosition_Sector obj=(BulletPosition_Sector)tree;
				obj.enabled=ba.ReadBool();
				obj.startAngle=ba.ReadFloat();
				obj.angle=ba.ReadFloat();
				obj.angleMode=(BulletAngleMode)ba.ReadInt();
			}
			private static void __firefxact_animation_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Animation obj=(FireFxAct_Animation)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.actionType=(AnimatorActionType)ba.ReadInt();
				obj.anim=ba.ReadInt();
				obj.paramName=ba.ReadInt();
				obj.paramType=(AnimatorControllerParameterType)ba.ReadInt();
				obj.paramValue=ba.ReadFloat();
				obj.animTarget=(AnimatorTarget)ba.ReadInt();
				obj.animPart=(AnimatorPart)ba.ReadInt();
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __firefxact_breakablewait_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_BreakableWait obj=(FireFxAct_BreakableWait)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationRange=ByteArrayTools.ReadVec2(ba);
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __firefxact_calcanimindex_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_CalcAnimIndex obj=(FireFxAct_CalcAnimIndex)tree;
				obj.enabled=ba.ReadBool();
				obj.interval=ba.ReadFloat();
				obj.maxActions=ba.ReadInt();
			}
			private static void __firefxact_changeweapon_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ChangeWeapon obj=(FireFxAct_ChangeWeapon)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __firefxact_chasemove_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ChaseMove obj=(FireFxAct_ChaseMove)tree;
				obj.enabled=ba.ReadBool();
				obj.velocityOffset=ByteArrayTools.ReadVec3(ba);
				obj.velocityMax=ByteArrayTools.ReadVec3(ba);
				obj.velocityMin=ByteArrayTools.ReadVec3(ba);
				obj.speed=ba.ReadFloat();
				obj.maxSpeed=ba.ReadFloat();
				obj.maxForce=ba.ReadFloat();
				obj.arriveThreshhold=ba.ReadFloat();
				obj.mass=ba.ReadFloat();
				obj.maxDuration=ba.ReadFloat();
				obj.orientMode=(OrientMode)ba.ReadInt();
				obj.speedScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __firefxact_clearcdonslot_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ClearCdOnSlot obj=(FireFxAct_ClearCdOnSlot)tree;
				obj.enabled=ba.ReadBool();
				obj.slot=ba.ReadInt();
				obj.mode=(SlotCdClearMode)ba.ReadInt();
			}
			private static void __firefxact_clearprevskillcd_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ClearPrevSkillCD obj=(FireFxAct_ClearPrevSkillCD)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_cleartargets_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ClearTargets obj=(FireFxAct_ClearTargets)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_correctorient_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_CorrectOrient obj=(FireFxAct_CorrectOrient)tree;
				obj.enabled=ba.ReadBool();
				obj.relation=(Relation)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.sightData=ByteArrayTools.Read<SightData>(ba,sp);
			}
			private static void __firefxact_dispatchevent_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_DispatchEvent obj=(FireFxAct_DispatchEvent)tree;
				obj.enabled=ba.ReadBool();
				obj.eventName=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __firefxact_fetchcontinueskill_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_FetchContinueSkill obj=(FireFxAct_FetchContinueSkill)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_fireskill_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_FireSkill obj=(FireFxAct_FireSkill)tree;
				obj.enabled=ba.ReadBool();
				obj.skillId=ba.ReadInt();
			}
			private static void __firefxact_getanimindex_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_GetAnimIndex obj=(FireFxAct_GetAnimIndex)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_globaltimescale_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_GlobalTimeScale obj=(FireFxAct_GlobalTimeScale)tree;
				obj.enabled=ba.ReadBool();
				obj.scale=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_limitattackorient_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_LimitAttackOrient obj=(FireFxAct_LimitAttackOrient)tree;
				obj.enabled=ba.ReadBool();
				obj.supportAngle=ba.ReadInt();
				obj.angleOffset=ba.ReadFloat();
			}
			private static void __firefxact_lookat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_LookAt obj=(FireFxAct_LookAt)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(FxLookMode)ba.ReadInt();
			}
			private static void __firefxact_lookattarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_LookAtTarget obj=(FireFxAct_LookAtTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.me=(SkillTarget)ba.ReadInt();
				obj.target=(SkillTarget)ba.ReadInt();
			}
			private static void __firefxact_markcdclearflag_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_MarkCDClearFlag obj=(FireFxAct_MarkCDClearFlag)tree;
				obj.enabled=ba.ReadBool();
				obj.clearable=ba.ReadBool();
			}
			private static void __firefxact_move_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Move obj=(FireFxAct_Move)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.ease=(EaseType)ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.angles=ByteArrayTools.ReadVec2(ba);
				obj.mode=(RushRunner.Mode)ba.ReadInt();
				obj.collideBuffId=ba.ReadInt();
				obj.source=(BuffPropertySource)ba.ReadInt();
			}
			private static void __firefxact_movetotarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_MoveToTarget obj=(FireFxAct_MoveToTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.speed=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.orient=ByteArrayTools.ReadVec2(ba);
				obj.distance=ByteArrayTools.ReadVec2(ba);
				obj.rvo=ba.ReadBool();
			}
			private static void __firefxact_npcsetinsteractive_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_NpcSetInsteractive obj=(FireFxAct_NpcSetInsteractive)tree;
				obj.enabled=ba.ReadBool();
				obj.interactive=ba.ReadBool();
				obj.visible=ba.ReadBool();
			}
			private static void __firefxact_radialblur_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_RadialBlur obj=(FireFxAct_RadialBlur)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.blur=ba.ReadFloat();
				obj.interval=ba.ReadFloat();
			}
			private static void __firefxact_randomcurve_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_RandomCurve obj=(FireFxAct_RandomCurve)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.speed=ba.ReadFloat();
				obj.radius=ba.ReadFloat();
				obj.pathType=(EasePathType)ba.ReadInt();
			}
			private static void __firefxact_setmode_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_SetMode obj=(FireFxAct_SetMode)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(ActionMode)ba.ReadInt();
				obj.targetType=(SkillTargetMask)ba.ReadInt();
			}
			private static void __firefxact_setskillextracd_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_SetSkillExtraCD obj=(FireFxAct_SetSkillExtraCD)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_settargetlimite_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_SetTargetLimite obj=(FireFxAct_SetTargetLimite)tree;
				obj.enabled=ba.ReadBool();
				obj.source=(BuffPropertySource)ba.ReadInt();
				obj.count=ba.ReadInt();
			}
			private static void __firefxact_shakesscreen_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_ShakesScreen obj=(FireFxAct_ShakesScreen)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.speed=ba.ReadFloat();
				obj.magnitude=ba.ReadFloat();
				obj.orthSizeOffset=ba.ReadFloat();
			}
			private static void __firefxact_sound_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Sound obj=(FireFxAct_Sound)tree;
				obj.enabled=ba.ReadBool();
				obj.asset=ByteArrayTools.ReadString(ba,sp);
				obj.soundPart=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __firefxact_stopcamerafollow_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_StopCameraFollow obj=(FireFxAct_StopCameraFollow)tree;
				obj.enabled=ba.ReadBool();
				obj.stopped=ba.ReadBool();
			}
			private static void __firefxact_summon_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Summon obj=(FireFxAct_Summon)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.machine=ByteArrayTools.ReadString(ba,sp);
				obj.qty=ba.ReadInt();
				obj.distanceRange=ByteArrayTools.ReadVec2(ba);
				obj.angleRange=ByteArrayTools.ReadVec2(ba);
				obj.symbiosis=ba.ReadBool();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_taunt_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Taunt obj=(FireFxAct_Taunt)tree;
				obj.enabled=ba.ReadBool();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.relate=(Relation)ba.ReadInt();
			}
			private static void __firefxact_tempantihit_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_TempAntiHit obj=(FireFxAct_TempAntiHit)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.antiHit=ba.ReadInt();
			}
			private static void __firefxact_timescale_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_TimeScale obj=(FireFxAct_TimeScale)tree;
				obj.enabled=ba.ReadBool();
				obj.scaleType=(TimeScaleType)ba.ReadInt();
				obj.scale=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
			}
			private static void __firefxact_turnorient_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_TurnOrient obj=(FireFxAct_TurnOrient)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
				obj.ease=(EaseType)ba.ReadInt();
				obj.angles=ByteArrayTools.ReadVec2(ba);
			}
			private static void __firefxact_tweentotarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_TweenToTarget obj=(FireFxAct_TweenToTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.target=(TargetType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.offset=ByteArrayTools.ReadVec3(ba);
			}
			private static void __firefxact_uieffect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_UIEffect obj=(FireFxAct_UIEffect)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __firefxact_unlockbullet_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_UnlockBullet obj=(FireFxAct_UnlockBullet)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __firefxact_waithitdone_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_WaitHitDone obj=(FireFxAct_WaitHitDone)tree;
				obj.enabled=ba.ReadBool();
				obj.target=(SkillTarget)ba.ReadInt();
			}
			private static void __firefxact_hitrebound_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_HitRebound obj=(FireFxAct_HitRebound)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.stun=ba.ReadFloat();
			}
			private static void __firefxact_onhithandler_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_OnHitHandler obj=(FireFxAct_OnHitHandler)tree;
				obj.enabled=ba.ReadBool();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
			}
			private static void __firefxact_shield_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_Shield obj=(FireFxAct_Shield)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
			}
			private static void __firefxact_skillprotect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				FireFxAct_SkillProtect obj=(FireFxAct_SkillProtect)tree;
				obj.enabled=ba.ReadBool();
				obj.sight=ByteArrayTools.Read<SightData>(ba,sp);
				obj.mode=(SkillProtectMode)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __sceneact_addprefab_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_AddPrefab obj=(SceneAct_AddPrefab)tree;
				obj.enabled=ba.ReadBool();
				obj.body=ByteArrayTools.ReadString(ba,sp);
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.offset=ByteArrayTools.ReadVec3(ba);
				obj.removeAsExit=ba.ReadBool();
			}
			private static void __sceneact_animation_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Animation obj=(SceneAct_Animation)tree;
				obj.enabled=ba.ReadBool();
				obj.actionType=(AnimatorActionType)ba.ReadInt();
				obj.anim=ba.ReadInt();
				obj.paramName=ba.ReadInt();
				obj.paramType=(AnimatorControllerParameterType)ba.ReadInt();
				obj.paramValue=ba.ReadFloat();
				obj.animPart=(AnimatorPart)ba.ReadInt();
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.forHost=ba.ReadBool();
			}
			private static void __sceneact_bubble_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Bubble obj=(SceneAct_Bubble)tree;
				obj.enabled=ba.ReadBool();
				obj.langKey=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.weight=ba.ReadFloat();
				obj.color=ByteArrayTools.ReadColor(ba);
				obj.size=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.source=ByteArrayTools.ReadString(ba,sp);
				obj.audio=ByteArrayTools.ReadString(ba,sp);
				obj.forHost=ba.ReadBool();
				obj.heroId=ba.ReadInt();
			}
			private static void __sceneact_changecpx_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_ChangeCpx obj=(SceneAct_ChangeCpx)tree;
				obj.enabled=ba.ReadBool();
				obj.machine=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_changesummoner_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_ChangeSummoner obj=(SceneAct_ChangeSummoner)tree;
				obj.enabled=ba.ReadBool();
				obj.clear=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.machine=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_deadanim_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_DeadAnim obj=(SceneAct_DeadAnim)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.maxAngle=ba.ReadInt();
			}
			private static void __sceneact_dispose_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Dispose obj=(SceneAct_Dispose)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __sceneact_fight_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Fight obj=(SceneAct_Fight)tree;
				obj.enabled=ba.ReadBool();
				obj.skillId=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.orientRange=ByteArrayTools.ReadVec2(ba);
				obj.isOrientOffset=ba.ReadBool();
				obj.loops=ba.ReadInt();
			}
			private static void __sceneact_lookat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_LookAt obj=(SceneAct_LookAt)tree;
				obj.enabled=ba.ReadBool();
				obj.targetObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.runMode=(RunMode)ba.ReadInt();
				obj.forHost=ba.ReadBool();
			}
			private static void __sceneact_playbullet_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_PlayBullet obj=(SceneAct_PlayBullet)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.interval=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.distance=ba.ReadFloat();
			}
			private static void __sceneact_playfx_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_PlayFx obj=(SceneAct_PlayFx)tree;
				obj.enabled=ba.ReadBool();
				obj.fx=ByteArrayTools.ReadString(ba,sp);
				obj.interval=ba.ReadFloat();
				obj.distanceRange=ByteArrayTools.ReadVec2(ba);
				obj.orientRange=ByteArrayTools.ReadVec2(ba);
				obj.isOrientOffset=ba.ReadBool();
				obj.loops=ba.ReadInt();
			}
			private static void __sceneact_playplot_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_PlayPlot obj=(SceneAct_PlayPlot)tree;
				obj.enabled=ba.ReadBool();
				obj.plot=ba.ReadInt();
			}
			private static void __sceneact_setchildactive_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetChildActive obj=(SceneAct_SetChildActive)tree;
				obj.enabled=ba.ReadBool();
				obj.childName=ByteArrayTools.ReadString(ba,sp);
				obj.active=ba.ReadBool();
			}
			private static void __sceneact_setfxtarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetFxTarget obj=(SceneAct_SetFxTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.targetObj=ByteArrayTools.Read<TargetObject>(ba,sp);
			}
			private static void __sceneact_setgameobjectactive_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetGameObjectActive obj=(SceneAct_SetGameObjectActive)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ba.ReadInt();
				obj.active=ba.ReadBool();
			}
			private static void __sceneact_setjumppath_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetJumpPath obj=(SceneAct_SetJumpPath)tree;
				obj.enabled=ba.ReadBool();
				obj.navId=ba.ReadInt();
				obj.path=ba.ReadInt();
				obj.dir=(NavDirection)ba.ReadInt();
			}
			private static void __sceneact_setocclusion_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetOcclusion obj=(SceneAct_SetOcclusion)tree;
				obj.enabled=ba.ReadBool();
				obj.value=ba.ReadBool();
			}
			private static void __sceneact_setweather_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetWeather obj=(SceneAct_SetWeather)tree;
				obj.enabled=ba.ReadBool();
				obj.weather=ba.ReadInt();
				obj.mode=(WeatherManager_Dll.UpdateMode)ba.ReadInt();
				obj.index=ba.ReadInt();
			}
			private static void __sceneact_sound_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Sound obj=(SceneAct_Sound)tree;
				obj.enabled=ba.ReadBool();
				obj.play=ba.ReadBool();
				obj.sound=ba.ReadInt();
			}
			private static void __sceneact_soundsource_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SoundSource obj=(SceneAct_SoundSource)tree;
				obj.enabled=ba.ReadBool();
				obj.asset=ByteArrayTools.ReadString(ba,sp);
				obj.soundPart=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_soundvolumn_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SoundVolumn obj=(SceneAct_SoundVolumn)tree;
				obj.enabled=ba.ReadBool();
				obj.sound=ba.ReadInt();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.volumn=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __sceneact_speakbasic_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SpeakBasic obj=(SceneAct_SpeakBasic)tree;
				obj.enabled=ba.ReadBool();
				obj.order=(SpeakOrder)ba.ReadInt();
				obj.interval=ByteArrayTools.ReadVec2(ba);
			}
			private static void __sceneact_speakpanel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SpeakPanel obj=(SceneAct_SpeakPanel)tree;
				obj.enabled=ba.ReadBool();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_stationactivate_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_StationActivate obj=(SceneAct_StationActivate)tree;
				obj.enabled=ba.ReadBool();
				obj.station=ba.ReadInt();
			}
			private static void __sceneact_stationpanel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_StationPanel obj=(SceneAct_StationPanel)tree;
				obj.enabled=ba.ReadBool();
				obj.station=ba.ReadInt();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_teleportactive_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_TeleportActive obj=(SceneAct_TeleportActive)tree;
				obj.enabled=ba.ReadBool();
				obj.teleport=ba.ReadInt();
				obj.active=ba.ReadBool();
			}
			private static void __sceneact_waitrebirth_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_WaitRebirth obj=(SceneAct_WaitRebirth)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenebuff_speed_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneBuff_Speed obj=(SceneBuff_Speed)tree;
				obj.enabled=ba.ReadBool();
				obj.ceoff=ba.ReadFloat();
			}
			private static void __scenecomposite_sequence_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneComposite_Sequence obj=(SceneComposite_Sequence)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __scenecomposite_sequenceselector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneComposite_SequenceSelector obj=(SceneComposite_SequenceSelector)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __scenecomposite_parallel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneComposite_Parallel obj=(SceneComposite_Parallel)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __scenecomposite_parallelselector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneComposite_ParallelSelector obj=(SceneComposite_ParallelSelector)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __scenedeco_repeat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_Repeat obj=(SceneDeco_Repeat)tree;
				obj.enabled=ba.ReadBool();
				obj.count=ba.ReadInt();
				obj.min=ba.ReadInt();
				obj.max=ba.ReadInt();
			}
			private static void __scenedeco_neverstop_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_NeverStop obj=(SceneDeco_NeverStop)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenedeco_wait_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_Wait obj=(SceneDeco_Wait)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.durationRange=ByteArrayTools.ReadVec2(ba);
				obj.durationScaleType=(TimeScaleType)ba.ReadInt();
			}
			private static void __scenedeco_untilsuccess_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_UntilSuccess obj=(SceneDeco_UntilSuccess)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenedeco_untilfailure_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_UntilFailure obj=(SceneDeco_UntilFailure)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenedeco_random_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneDeco_Random obj=(SceneDeco_Random)tree;
				obj.enabled=ba.ReadBool();
				obj.method=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __sceneact_camelacticity_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CamElacticity obj=(SceneAct_CamElacticity)tree;
				obj.enabled=ba.ReadBool();
				obj.elaticity=ba.ReadFloat();
			}
			private static void __sceneact_camlock_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CamLock obj=(SceneAct_CamLock)tree;
				obj.enabled=ba.ReadBool();
				obj.lockCam=ba.ReadBool();
			}
			private static void __sceneact_camorthsize_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CamOrthSize obj=(SceneAct_CamOrthSize)tree;
				obj.enabled=ba.ReadBool();
				obj.size=ba.ReadFloat();
				obj.duration=ba.ReadFloat();
			}
			private static void __sceneact_camrelease_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CamRelease obj=(SceneAct_CamRelease)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __sceneact_camtween_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CamTween obj=(SceneAct_CamTween)tree;
				obj.enabled=ba.ReadBool();
				obj.from=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.to=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.percent=ba.ReadFloat();
				obj.startFromCurrent=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.ease=(EaseType)ba.ReadInt();
			}
			private static void __scenecon_actionmode_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_ActionMode obj=(SceneCon_ActionMode)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(ActionMode)ba.ReadInt();
				obj.value=ba.ReadBool();
			}
			private static void __scenecon_career_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_Career obj=(SceneCon_Career)tree;
				obj.enabled=ba.ReadBool();
				obj.careerId=ba.ReadInt();
			}
			private static void __scenecon_distancecompare_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_DistanceCompare obj=(SceneCon_DistanceCompare)tree;
				obj.enabled=ba.ReadBool();
				obj.targetObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.threshold=ba.ReadFloat();
				obj.untilSuccess=ba.ReadBool();
			}
			private static void __scenecon_gender_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_Gender obj=(SceneCon_Gender)tree;
				obj.enabled=ba.ReadBool();
				obj.gender=(Gender)ba.ReadInt();
			}
			private static void __scenecon_hasmission_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_HasMission obj=(SceneCon_HasMission)tree;
				obj.enabled=ba.ReadBool();
				obj.missionId=ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.missionStatus=(MissionStatus)ba.ReadInt();
				obj.untilSuccess=ba.ReadBool();
				obj.debug=ba.ReadBool();
				obj.useDebug=ba.ReadBool();
			}
			private static void __scenecon_hasscene_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_HasScene obj=(SceneCon_HasScene)tree;
				obj.enabled=ba.ReadBool();
				obj.sceneId=ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.debug=ba.ReadBool();
			}
			private static void __scenecon_hasstation_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_HasStation obj=(SceneCon_HasStation)tree;
				obj.enabled=ba.ReadBool();
				obj.station=ba.ReadInt();
				obj.untilSuccess=ba.ReadBool();
				obj.compare=ba.ReadBool();
				obj.debug=ba.ReadBool();
				obj.debugValue=ba.ReadBool();
			}
			private static void __scenecon_hasthing_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_HasThing obj=(SceneCon_HasThing)tree;
				obj.enabled=ba.ReadBool();
				obj.source=ByteArrayTools.ReadString(ba,sp);
				obj.sourceId=ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
				obj.debug=ba.ReadBool();
			}
			private static void __scenecon_hero_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_Hero obj=(SceneCon_Hero)tree;
				obj.enabled=ba.ReadBool();
				obj.heroId=ba.ReadInt();
			}
			private static void __scenecon_navlayercheck_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_NavLayerCheck obj=(SceneCon_NavLayerCheck)tree;
				obj.enabled=ba.ReadBool();
				obj.forHost=ba.ReadBool();
				obj.layer=ba.ReadInt();
				obj.value=ba.ReadBool();
				obj.untilSuccess=ba.ReadBool();
			}
			private static void __scenecon_neareatobj_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_NeareatObj obj=(SceneCon_NeareatObj)tree;
				obj.enabled=ba.ReadBool();
				obj.forHost=ba.ReadBool();
				obj.list=ByteArrayTools.ReadArray<int>(ba,sp);
				obj.index=ba.ReadInt();
			}
			private static void __scenecon_npcenteridlecount_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_NpcEnterIdleCount obj=(SceneCon_NpcEnterIdleCount)tree;
				obj.enabled=ba.ReadBool();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.count=ba.ReadInt();
			}
			private static void __scenecon_property_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneCon_Property obj=(SceneCon_Property)tree;
				obj.enabled=ba.ReadBool();
				obj.sourceType=(PropertySource)ba.ReadInt();
				obj.target=(TargetType)ba.ReadInt();
				obj.property=ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
				obj.isPercent=ba.ReadBool();
				obj.debug=ba.ReadBool();
				obj.useDebug=ba.ReadBool();
			}
			private static void __sceneact_collideplant_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CollidePlant obj=(SceneAct_CollidePlant)tree;
				obj.enabled=ba.ReadBool();
				obj.plant=ba.ReadInt();
			}
			private static void __sceneact_dissovle_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Dissovle obj=(SceneAct_Dissovle)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.show=ba.ReadBool();
			}
			private static void __sceneact_groundcolor_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_GroundColor obj=(SceneAct_GroundColor)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.ground=ba.ReadInt();
				obj.loops=ba.ReadInt();
				obj.color=ByteArrayTools.ReadColor(ba);
			}
			private static void __sceneact_setfooteffect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetFootEffect obj=(SceneAct_SetFootEffect)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
			}
			private static void __sceneact_setfootprint_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetFootPrint obj=(SceneAct_SetFootPrint)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.autoDispose=ba.ReadBool();
			}
			private static void __sceneact_setscratch_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetScratch obj=(SceneAct_SetScratch)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.autoDispose=ba.ReadBool();
			}
			private static void __sceneact_setshadow_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetShadow obj=(SceneAct_SetShadow)tree;
				obj.enabled=ba.ReadBool();
				obj.value=ba.ReadBool();
			}
			private static void __scenefx_colormatrix_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneFx_ColorMatrix obj=(SceneFx_ColorMatrix)tree;
				obj.enabled=ba.ReadBool();
				obj.camera=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.loops=ba.ReadInt();
				obj.color=ByteArrayTools.Read<ColorMatrix>(ba,sp);
			}
			private static void __scenefx_dispertile_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneFx_DisperTile obj=(SceneFx_DisperTile)tree;
				obj.enabled=ba.ReadBool();
				obj.layer=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.value=ByteArrayTools.ReadVec2(ba);
				obj.easeType=(EaseType)ba.ReadInt();
				obj.activeAtEnd=ba.ReadBool();
			}
			private static void __sceneact_curvemove_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CurveMove obj=(SceneAct_CurveMove)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.curve=ba.ReadInt();
				obj.range=ByteArrayTools.ReadVec2(ba);
				obj.count=ba.ReadInt();
				obj.wrap=(TweenWrapMode)ba.ReadInt();
			}
			private static void __sceneact_gototarget_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_GotoTarget obj=(SceneAct_GotoTarget)tree;
				obj.enabled=ba.ReadBool();
				obj.targetObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.distance=ba.ReadFloat();
				obj.useModelSpeed=ba.ReadBool();
				obj.speed=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.forceXAxis=ba.ReadBool();
			}
			private static void __sceneact_jump_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Jump obj=(SceneAct_Jump)tree;
				obj.enabled=ba.ReadBool();
				obj.navId=ba.ReadInt();
				obj.path=ba.ReadInt();
				obj.dir=(NavJumpDir)ba.ReadInt();
			}
			private static void __sceneact_linearchase_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_LinearChase obj=(SceneAct_LinearChase)tree;
				obj.enabled=ba.ReadBool();
				obj.runner=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.src=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.srcOffset=ByteArrayTools.ReadVec3(ba);
				obj.dst=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.dstOffset=ByteArrayTools.ReadVec3(ba);
				obj.speed=ba.ReadFloat();
				obj.useSelfSpeed=ba.ReadBool();
				obj.threshold=ba.ReadFloat();
				obj.orient=(OrientMode)ba.ReadInt();
				obj.keepFollow=ba.ReadBool();
			}
			private static void __sceneact_path_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Path obj=(SceneAct_Path)tree;
				obj.enabled=ba.ReadBool();
				obj.path=ba.ReadInt();
				obj.speed=ba.ReadFloat();
				obj.forHost=ba.ReadBool();
				obj.useModelSpeed=ba.ReadBool();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.useRvo=ba.ReadBool();
				obj.resumeInterval=ba.ReadFloat();
			}
			private static void __sceneact_position_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_Position obj=(SceneAct_Position)tree;
				obj.enabled=ba.ReadBool();
				obj.location=ba.ReadInt();
				obj.forHost=ba.ReadBool();
			}
			private static void __scenetween_color_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Color obj=(SceneTween_Color)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.start=ByteArrayTools.ReadColor(ba);
				obj.end=ByteArrayTools.ReadColor(ba);
				obj.easeType=(EaseType)ba.ReadInt();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.loops=ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __scenetween_lookat_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_LookAt obj=(SceneTween_LookAt)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.loops=ba.ReadInt();
				obj.forHost=ba.ReadBool();
				obj.instant=ba.ReadBool();
			}
			private static void __scenetween_orient_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Orient obj=(SceneTween_Orient)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.orient=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.forHost=ba.ReadBool();
				obj.isOffset=ba.ReadBool();
				obj.instant=ba.ReadBool();
			}
			private static void __scenetween_path_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Path obj=(SceneTween_Path)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.path=ba.ReadInt();
				obj.loops=ba.ReadInt();
				obj.direction=(DirectionMode)ba.ReadInt();
				obj.pathType=(EasePathType)ba.ReadInt();
				obj.forHost=ba.ReadBool();
				obj.head2Tail=ba.ReadBool();
			}
			private static void __scenetween_position_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Position obj=(SceneTween_Position)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.translate=ByteArrayTools.ReadVec3(ba);
				obj.relative=ba.ReadBool();
				obj.loops=ba.ReadInt();
				obj.forHost=ba.ReadBool();
			}
			private static void __scenetween_scale_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Scale obj=(SceneTween_Scale)tree;
				obj.enabled=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.scale=ba.ReadFloat();
				obj.loops=ba.ReadInt();
				obj.forHost=ba.ReadBool();
			}
			private static void __scenetween_translate_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_Translate obj=(SceneTween_Translate)tree;
				obj.enabled=ba.ReadBool();
				obj.srcObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.srcOffset=ByteArrayTools.ReadVec3(ba);
				obj.dstObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.dstOffset=ByteArrayTools.ReadVec3(ba);
				obj.duration=ba.ReadFloat();
				obj.wrapMode=(TweenWrapMode)ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.loops=ba.ReadInt();
			}
			private static void __scenetween_tweenchildposition_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneTween_TweenChildPosition obj=(SceneTween_TweenChildPosition)tree;
				obj.enabled=ba.ReadBool();
				obj.part=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.translate=ByteArrayTools.ReadVec3(ba);
				obj.relative=ba.ReadBool();
			}
			private static void __sceneact_lockio_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_LockIO obj=(SceneAct_LockIO)tree;
				obj.enabled=ba.ReadBool();
				obj.locked=ba.ReadBool();
			}
			private static void __sceneact_waitbutton_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_WaitButton obj=(SceneAct_WaitButton)tree;
				obj.enabled=ba.ReadBool();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.func=ByteArrayTools.ReadString(ba,sp);
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __scenenpc_adhere_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_Adhere obj=(SceneNpc_Adhere)tree;
				obj.enabled=ba.ReadBool();
				obj.isOn=ba.ReadBool();
				obj.targetObj=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.attachMode=(TransformAdhesion)ba.ReadInt();
			}
			private static void __scenenpc_callmakeinteractivedrop_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_CallMakeInteractiveDrop obj=(SceneNpc_CallMakeInteractiveDrop)tree;
				obj.enabled=ba.ReadBool();
				obj.lootId=ba.ReadInt();
			}
			private static void __scenenpc_callnpcfunc_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_CallNpcFunc obj=(SceneNpc_CallNpcFunc)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenenpc_centerwander_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_CenterWander obj=(SceneNpc_CenterWander)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.radius=ba.ReadFloat();
				obj.speed=ba.ReadFloat();
				obj.useModelSpeed=ba.ReadBool();
			}
			private static void __scenenpc_changeactionsource_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ChangeActionSource obj=(SceneNpc_ChangeActionSource)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(ActionType)ba.ReadInt();
				obj.phase=(ActionPhase)ba.ReadInt();
				obj.action=ba.ReadInt();
			}
			private static void __scenenpc_changemode_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ChangeMode obj=(SceneNpc_ChangeMode)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(ActionMode)ba.ReadInt();
			}
			private static void __scenenpc_changerelatioin_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ChangeRelatioin obj=(SceneNpc_ChangeRelatioin)tree;
				obj.enabled=ba.ReadBool();
				obj.relation=(Relation)ba.ReadInt();
			}
			private static void __scenenpc_changethink_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ChangeThink obj=(SceneNpc_ChangeThink)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(ThinkPhase)ba.ReadInt();
				obj.think=ba.ReadInt();
			}
			private static void __scenenpc_clearactioncache_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_ClearActionCache obj=(SceneNpc_ClearActionCache)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(ActionPhase)ba.ReadInt();
				obj.mode=(ActionType)ba.ReadInt();
			}
			private static void __scenenpc_fightwander_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_FightWander obj=(SceneNpc_FightWander)tree;
				obj.enabled=ba.ReadBool();
				obj.dir=(DirectionMode)ba.ReadInt();
				obj.timeRange=ByteArrayTools.ReadVec2(ba);
				obj.speed=ba.ReadFloat();
			}
			private static void __scenenpc_ismode_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_IsMode obj=(SceneNpc_IsMode)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(ActionMode)ba.ReadInt();
				obj.equal=ba.ReadBool();
			}
			private static void __scenenpc_movetobirthplace_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_MoveToBirthPlace obj=(SceneNpc_MoveToBirthPlace)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenenpc_opendropbox_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_OpenDropBox obj=(SceneNpc_OpenDropBox)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenenpc_openfuncpanel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_OpenFuncPanel obj=(SceneNpc_OpenFuncPanel)tree;
				obj.enabled=ba.ReadBool();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.waitClose=ba.ReadBool();
			}
			private static void __scenenpc_openpanel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_OpenPanel obj=(SceneNpc_OpenPanel)tree;
				obj.enabled=ba.ReadBool();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.waitClose=ba.ReadBool();
			}
			private static void __scenenpc_patrolarea_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_PatrolArea obj=(SceneNpc_PatrolArea)tree;
				obj.enabled=ba.ReadBool();
				obj.area=ba.ReadInt();
				obj.excludes=ByteArrayTools.ReadArray<int>(ba,sp);
			}
			private static void __scenenpc_patrolpath_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_PatrolPath obj=(SceneNpc_PatrolPath)tree;
				obj.enabled=ba.ReadBool();
				obj.path=ba.ReadInt();
				obj.forward=ba.ReadBool();
			}
			private static void __scenenpc_patrolrange_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_PatrolRange obj=(SceneNpc_PatrolRange)tree;
				obj.enabled=ba.ReadBool();
				obj.radius=ba.ReadFloat();
			}
			private static void __scenenpc_peronalityproperty_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_PeronalityProperty obj=(SceneNpc_PeronalityProperty)tree;
				obj.enabled=ba.ReadBool();
				obj.firm=ba.ReadInt();
				obj.voluntary=ba.ReadInt();
				obj.justice=ba.ReadInt();
				obj.guard=ba.ReadInt();
				obj.alarm=ba.ReadInt();
				obj.duty=ba.ReadInt();
				obj.seekHelp=ba.ReadInt();
				obj.help=ba.ReadInt();
				obj.survival=ba.ReadInt();
				obj.honor=ba.ReadInt();
			}
			private static void __scenenpc_setbirthplace_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_SetBirthPlace obj=(SceneNpc_SetBirthPlace)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenenpc_setinteractive_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_SetInteractive obj=(SceneNpc_SetInteractive)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(SceneNpc_SetInteractive.Mode)ba.ReadInt();
				obj.interactive=ba.ReadBool();
				obj.visible=ba.ReadBool();
			}
			private static void __scenenpc_setnearbyarea_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_SetNearByArea obj=(SceneNpc_SetNearByArea)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(RangeMode)ba.ReadInt();
				obj.radius=ba.ReadFloat();
				obj.area=ba.ReadInt();
			}
			private static void __scenenpc_setnearbyprefab_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_SetNearByPrefab obj=(SceneNpc_SetNearByPrefab)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __scenenpccon_magicbox_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpcCon_MagicBox obj=(SceneNpcCon_MagicBox)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __scenenpc_useskill_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_UseSkill obj=(SceneNpc_UseSkill)tree;
				obj.enabled=ba.ReadBool();
				obj.skill=ba.ReadInt();
			}
			private static void __scenenpc_waitpanelclose_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneNpc_WaitPanelClose obj=(SceneNpc_WaitPanelClose)tree;
				obj.enabled=ba.ReadBool();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.waitOpenDuration=ba.ReadFloat();
			}
			private static void __sceneact_collidesetting_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CollideSetting obj=(SceneAct_CollideSetting)tree;
				obj.enabled=ba.ReadBool();
				obj.mode=(SceneAct_CollideSetting.Mode)ba.ReadInt();
				obj.weight=ba.ReadFloat();
				obj.collideLevel=ba.ReadInt();
				obj.orientInterpolate=ba.ReadFloat();
			}
			private static void __sceneact_countonnavlayer_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CountOnNavLayer obj=(SceneAct_CountOnNavLayer)tree;
				obj.enabled=ba.ReadBool();
				obj.layer=ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.count=ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.excludeList=ByteArrayTools.ReadArray<TargetObject>(ba,sp);
				obj.untilSuccess=ba.ReadBool();
			}
			private static void __sceneact_onnavlayer_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_OnNavLayer obj=(SceneAct_OnNavLayer)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.layer=ba.ReadInt();
				obj.untilSuccess=ba.ReadBool();
				obj.isOn=ba.ReadBool();
			}
			private static void __sceneact_setnavlayer_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetNavLayer obj=(SceneAct_SetNavLayer)tree;
				obj.enabled=ba.ReadBool();
				obj.layer=ba.ReadInt();
			}
			private static void __sceneact_setrvo_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_SetRvo obj=(SceneAct_SetRvo)tree;
				obj.enabled=ba.ReadBool();
				obj.action=(PhysicMode)ba.ReadInt();
				obj.useGravity=ba.ReadBool();
				obj.useGeometry=ba.ReadBool();
				obj.useRvo=ba.ReadBool();
				obj.useCollide=ba.ReadBool();
			}
			private static void __sceneplot_actor_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_Actor obj=(ScenePlot_Actor)tree;
				obj.enabled=ba.ReadBool();
				obj.host=ba.ReadBool();
				obj.actor=ba.ReadInt();
				obj.lockHost=ba.ReadBool();
			}
			private static void __sceneplot_filmscreen_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_FilmScreen obj=(ScenePlot_FilmScreen)tree;
				obj.enabled=ba.ReadBool();
				obj.openPanel=ba.ReadBool();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneplot_panel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_Panel obj=(ScenePlot_Panel)tree;
				obj.enabled=ba.ReadBool();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneplot_panelcontent_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_PanelContent obj=(ScenePlot_PanelContent)tree;
				obj.enabled=ba.ReadBool();
				obj.langKey=ba.ReadInt();
				obj.audio=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.interval=ba.ReadFloat();
				obj.size=ba.ReadInt();
				obj.color=ByteArrayTools.ReadColor(ba);
				obj.host=ba.ReadBool();
				obj.npcId=ba.ReadInt();
				obj.left=ba.ReadBool();
				obj.heroId=ba.ReadInt();
			}
			private static void __sceneplot_targetactor_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_TargetActor obj=(ScenePlot_TargetActor)tree;
				obj.enabled=ba.ReadBool();
				obj.target=ByteArrayTools.Read<TargetObject>(ba,sp);
				obj.lockHost=ba.ReadBool();
			}
			private static void __sceneplot_targetactorgroup_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				ScenePlot_TargetActorGroup obj=(ScenePlot_TargetActorGroup)tree;
				obj.enabled=ba.ReadBool();
				obj.targets=ByteArrayTools.ReadArray<TargetObject>(ba,sp);
				obj.lockHost=ba.ReadBool();
			}
			private static void __sceneact_activatemission_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_ActivateMission obj=(SceneAct_ActivateMission)tree;
				obj.enabled=ba.ReadBool();
				obj.missionId=ba.ReadInt();
			}
			private static void __sceneact_callbuildingbuff_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CallBuildingBuff obj=(SceneAct_CallBuildingBuff)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(BuffPhase)ba.ReadInt();
			}
			private static void __sceneact_callgather_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CallGather obj=(SceneAct_CallGather)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __sceneact_callpathfinish_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_CallPathFinish obj=(SceneAct_CallPathFinish)tree;
				obj.enabled=ba.ReadBool();
				obj.path=ba.ReadInt();
			}
			private static void __sceneact_enterarea_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_EnterArea obj=(SceneAct_EnterArea)tree;
				obj.enabled=ba.ReadBool();
				obj.region=ba.ReadInt();
			}
			private static void __sceneact_randomteleport_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_RandomTeleport obj=(SceneAct_RandomTeleport)tree;
				obj.enabled=ba.ReadBool();
				obj.moduleId=ba.ReadInt();
			}
			private static void __sceneact_uieffect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_UIEffect obj=(SceneAct_UIEffect)tree;
				obj.enabled=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_uishowhide_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				SceneAct_UIShowHide obj=(SceneAct_UIShowHide)tree;
				obj.enabled=ba.ReadBool();
				obj.show=ba.ReadBool();
			}
			private static void __personalitycomposition_seq_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PersonalityComposition_Seq obj=(PersonalityComposition_Seq)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __personalitycomposition_pal_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PersonalityComposition_Pal obj=(PersonalityComposition_Pal)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __personalitycomposition_selector_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PersonalityComposition_Selector obj=(PersonalityComposition_Selector)tree;
				obj.enabled=ba.ReadBool();
				obj.time=ba.ReadFloat();
				obj.timeScaleType=(TimeScaleType)ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
			}
			private static void __personalitydeco_random_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PersonalityDeco_Random obj=(PersonalityDeco_Random)tree;
				obj.enabled=ba.ReadBool();
				obj.method=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __personality_iteralarmmessage_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_IterAlarmMessage obj=(Personality_IterAlarmMessage)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_iterattackmessage_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_IterAttackMessage obj=(Personality_IterAttackMessage)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_iterhelpmessage_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_IterHelpMessage obj=(Personality_IterHelpMessage)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_itertargetinsight_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_IterTargetInSight obj=(Personality_IterTargetInSight)tree;
				obj.enabled=ba.ReadBool();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
			}
			private static void __personality_onhit_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_OnHit obj=(Personality_OnHit)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_onmode_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_OnMode obj=(Personality_OnMode)tree;
				obj.enabled=ba.ReadBool();
				obj.action=(ActionMode)ba.ReadInt();
			}
			private static void __personality_property_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_Property obj=(Personality_Property)tree;
				obj.enabled=ba.ReadBool();
				obj.propertyId=ba.ReadInt();
				obj.targetType=(ActionTarget)ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.value=ba.ReadFloat();
			}
			private static void __personality_attack_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_Attack obj=(Personality_Attack)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_callhelp_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_CallHelp obj=(Personality_CallHelp)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_callpolice_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_CallPolice obj=(Personality_CallPolice)tree;
				obj.enabled=ba.ReadBool();
				obj.count=ba.ReadInt();
			}
			private static void __personality_flee_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_Flee obj=(Personality_Flee)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_gohelp_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_GoHelp obj=(Personality_GoHelp)tree;
				obj.enabled=ba.ReadBool();
			}
			private static void __personality_investigate_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Personality_Investigate obj=(Personality_Investigate)tree;
				obj.enabled=ba.ReadBool();
				obj.count=ba.ReadInt();
			}
			private static void __dialog_button_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Dialog_Button obj=(Dialog_Button)tree;
				obj.enabled=ba.ReadBool();
				obj.phase=(SpeakPhase)ba.ReadInt();
				obj.style=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __dialog_content_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Dialog_Content obj=(Dialog_Content)tree;
				obj.enabled=ba.ReadBool();
				obj.langKey=ba.ReadInt();
				obj.audio=ByteArrayTools.ReadString(ba,sp);
				obj.heroId=ba.ReadInt();
				obj.drawIcon=ba.ReadBool();
				obj.icon=ByteArrayTools.ReadString(ba,sp);
				obj.nameIcon=ByteArrayTools.ReadString(ba,sp);
				obj.forHost=ba.ReadBool();
			}
			private static void __dialog_group_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				Dialog_Group obj=(Dialog_Group)tree;
				obj.enabled=ba.ReadBool();
				obj.langKey=ba.ReadInt();
				obj.nodeType=ba.ReadInt();
				obj.weight=ba.ReadFloat();
			}
			private static void __panelsetting_childnormal_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_ChildNormal obj=(PanelSetting_ChildNormal)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
			}
			private static void __panelsetting_childmanual_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_ChildManual obj=(PanelSetting_ChildManual)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
			}
			private static void __panelsetting_childselect_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_ChildSelect obj=(PanelSetting_ChildSelect)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.control=ByteArrayTools.ReadString(ba,sp);
				obj.start=ba.ReadInt();
			}
			private static void __panelsetting_childstack_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_ChildStack obj=(PanelSetting_ChildStack)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.control=ByteArrayTools.ReadString(ba,sp);
				obj.closeAsStackEmpty=ba.ReadBool();
				obj.autoOpenFirstChild=ba.ReadBool();
			}
			private static void __panelsetting_autoclose_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_AutoClose obj=(PanelSetting_AutoClose)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.policy=(ClosePolicy)ba.ReadInt();
				obj.exceptions=ByteArrayTools.ReadArray<string>(ba,sp);
			}
			private static void __panelsetting_duration_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_Duration obj=(PanelSetting_Duration)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.duration=ba.ReadFloat();
			}
			private static void __panelsetting_lua_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_Lua obj=(PanelSetting_Lua)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __panelsetting_opentoobject_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_OpenToObject obj=(PanelSetting_OpenToObject)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
			}
			private static void __panelsetting_cull_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_Cull obj=(PanelSetting_Cull)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.mode=(PanelCulling)ba.ReadInt();
			}
			private static void __panelsetting_tweenscreen_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_TweenScreen obj=(PanelSetting_TweenScreen)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.clicker=ByteArrayTools.ReadString(ba,sp);
				obj.groupKey=ByteArrayTools.ReadString(ba,sp);
				obj.begin=ba.ReadBool();
			}
			private static void __panelsetting_animationonly_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_AnimationOnly obj=(PanelSetting_AnimationOnly)tree;
				obj.enabled=ba.ReadBool();
				obj.showPhase=(ShowHidePhase)ba.ReadInt();
				obj.hidePhase=(ShowHidePhase)ba.ReadInt();
				obj.animation=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __panelsetting_module_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_Module obj=(PanelSetting_Module)tree;
				obj.enabled=ba.ReadBool();
				obj.module=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __panelsetting_panel_reader__(IByteReader ba, IList<string>sp, TreeCore tree){
				PanelSetting_Panel obj=(PanelSetting_Panel)tree;
				obj.enabled=ba.ReadBool();
				obj.mediatorName=ByteArrayTools.ReadString(ba,sp);
				obj.mediatorType=(MediatorType)ba.ReadInt();
				obj.automatic=(PanelAutomatic)ba.ReadInt();
				obj.layer=ByteArrayTools.ReadString(ba,sp);
				obj.sceneType=(SceneType)ba.ReadInt();
				obj.lifeMode=(AssetLife)ba.ReadInt();
				obj.extraPrefab=ba.ReadBool();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.loadInstant=ba.ReadBool();
				obj.prefabIsRoot=ba.ReadBool();
				obj.disableOnDebuging=ba.ReadBool();
			}
	}
}
