﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using System;
using System.Collections.Generic;
using System.Reflection;


namespace XLua.CSObjectWrap
{
    public class XLua_Gen_Initer_Register__	{
        
        
        static void wrapInit0(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(object), SystemObjectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Object), UnityEngineObjectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Vector2), UnityEngineVector2Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Vector3), UnityEngineVector3Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Vector4), UnityEngineVector4Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Quaternion), UnityEngineQuaternionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Color), UnityEngineColorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Ray), UnityEngineRayWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Bounds), UnityEngineBoundsWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Ray2D), UnityEngineRay2DWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Time), UnityEngineTimeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.GameObject), UnityEngineGameObjectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Component), UnityEngineComponentWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Behaviour), UnityEngineBehaviourWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Transform), UnityEngineTransformWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Resources), UnityEngineResourcesWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.TextAsset), UnityEngineTextAssetWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Keyframe), UnityEngineKeyframeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.AnimationCurve), UnityEngineAnimationCurveWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.AnimationClip), UnityEngineAnimationClipWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.MonoBehaviour), UnityEngineMonoBehaviourWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.ParticleSystem), UnityEngineParticleSystemWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.SkinnedMeshRenderer), UnityEngineSkinnedMeshRendererWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Renderer), UnityEngineRendererWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Networking.UnityWebRequest), UnityEngineNetworkingUnityWebRequestWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Light), UnityEngineLightWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Mathf), UnityEngineMathfWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(System.Collections.Generic.List<int>), SystemCollectionsGenericList_1_SystemInt32_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Debug), UnityEngineDebugWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(System.Collections.Generic.IList<object>), SystemCollectionsGenericIList_1_SystemObject_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(System.Collections.Generic.IList<double>), SystemCollectionsGenericIList_1_SystemDouble_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.utils.UIColorGroup), monouiutilsUIColorGroupWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.utils.UITween), monouiutilsUITweenWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.space.RTEffect), monouispaceRTEffectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.space.RTSpace), monouispaceRTSpaceWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.joystick.SkillControl), monouijoystickSkillControlWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.AudioIndexer), monouielementsAudioIndexerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Bar), monouielementsBarWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Clickable), monouielementsClickableWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Draggable), monouielementsDraggableWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Dropable), monouielementsDropableWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GradientText), monouielementsGradientTextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupActive), monouielementsGroupActiveWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupBoolean), monouielementsGroupBooleanWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupColor), monouielementsGroupColorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupFloat), monouielementsGroupFloatWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupIndexVisible), monouielementsGroupIndexVisibleWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupInt), monouielementsGroupIntWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.GroupString), monouielementsGroupStringWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.ImageBlendMode), monouielementsImageBlendModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.MovieClip), monouielementsMovieClipWrap.__Register);
        
        }
        
        static void wrapInit1(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.MultiBlurMask), monouielementsMultiBlurMaskWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.NumberField), monouielementsNumberFieldWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.OversizeImage), monouielementsOversizeImageWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PButton), monouielementsPButtonWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PDropdown), monouielementsPDropdownWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PImage), monouielementsPImageWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PolyImage), monouielementsPolyImageWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PortraitSetting), monouielementsPortraitSettingWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PRepeatImage), monouielementsPRepeatImageWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Pressable), monouielementsPressableWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.PText), monouielementsPTextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.RichText), monouielementsRichTextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Star), monouielementsStarWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.Tab), monouielementsTabWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.TouchableRect), monouielementsTouchableRectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.elements.WorldMap), monouielementsWorldMapWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.ComoboList), monouicontrolsComoboListWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIBackground), monouicontrolsUIBackgroundWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIBar), monouicontrolsUIBarWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIButton), monouicontrolsUIButtonWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UICheckBox), monouicontrolsUICheckBoxWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UICombobox), monouicontrolsUIComboboxWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIDataPane), monouicontrolsUIDataPaneWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIEffect), monouicontrolsUIEffectWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIItemPane), monouicontrolsUIItemPaneWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UILabel), monouicontrolsUILabelWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIPortrait), monouicontrolsUIPortraitWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIPrefabReplace), monouicontrolsUIPrefabReplaceWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIRadioButton), monouicontrolsUIRadioButtonWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIResourceField), monouicontrolsUIResourceFieldWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIRichText), monouicontrolsUIRichTextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UISlider), monouicontrolsUISliderWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UIStar), monouicontrolsUIStarWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UITabNavigator), monouicontrolsUITabNavigatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UITextArea), monouicontrolsUITextAreaWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.ui.controls.UITextInput), monouicontrolsUITextInputWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.scene.NpcRegion), monosceneNpcRegionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.scene.Region), monosceneRegionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.scene.Teleport), monosceneTeleportWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.data.NpcFuncBBDelegate), monodataNpcFuncBBDelegateWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.data.NpcFuncSell), monodataNpcFuncSellWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.data.NpcFuncShop), monodataNpcFuncShopWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.data.NpcFuncWash), monodataNpcFuncWashWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(mono.data.RebirthArea), monodataRebirthAreaWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.UI.AnimatorController), dlUIAnimatorControllerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.UI.BossBlood), dlUIBossBloodWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.UI.BossBloodSprite), dlUIBossBloodSpriteWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.UI.EventGroup), dlUIEventGroupWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.sequence.battle.BattleController), dlsequencebattleBattleControllerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.runtime.DeviceInfo), dlruntimeDeviceInfoWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.mono.ui.SeqImage), dlmonouiSeqImageWrap.__Register);
        
        }
        
        static void wrapInit2(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(dl.lua.BridgeLuaScene), dlluaBridgeLuaSceneWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.entity.impl.EntityVisibiltyManager), dlentityimplEntityVisibiltyManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.FlyTextManager), gameuiFlyTextManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.LogManager), gameuiLogManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.MapManager), gameuiMapManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ItemPane_Dll), gameuiItemPane_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ListAction), gameuiListActionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ListEvent), gameuiListEventWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ScrollVO), gameuiScrollVOWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UIDataPane_Dll), gameuiUIDataPane_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.RichEmoFactory), gameuiRichEmoFactoryWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.MapIcon_Lua), gameuiMapIcon_LuaWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.SelectableItemBase), gameuiSelectableItemBaseWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UIRendererBase), gameuiUIRendererBaseWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.PDropable_Dll), gamemonoPDropable_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.range.SightData), gamemonorangeSightDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.manager.SceneSpecialMode), gamemonomanagerSceneSpecialModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.manager.SceneManager), gamemonomanagerSceneManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.data.npc.NpcItem), gamemonodatanpcNpcItemWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.avatar.AvatarCondition), gamemonoavatarAvatarConditionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.LuaEntity), gameaientityLuaEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.ModelEntity), gameaientityModelEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.OperationCode), gameaientityOperationCodeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.OperationVO), gameaientityOperationVOWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.avatar.RenderPartPrefab), gameaientityavatarRenderPartPrefabWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.entity.avatar.RenderSlot), gameaientityavatarRenderSlotWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.personality.NpcPropertyUnit), gameaipersonalityNpcPropertyUnitWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.action.htask.HostTaskManager), gameaimachineactionhtaskHostTaskManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.action.htask.PathGuideManager), gameaimachineactionhtaskPathGuideManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.AnimVO), gameaimachinevoAnimVOWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.DamageCalc), gameaimachinevoDamageCalcWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.SkillCeoffModifier), gameaimachinevoSkillCeoffModifierWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.SkillRetrieverInfo), gameaimachinevoSkillRetrieverInfoWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.FxBuffVO), gameaimachinevoFxBuffVOWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.ComboVO), gameaimachinevoComboVOWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.machine.vo.FightResult), gameaimachinevoFightResultWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_AIMode), gameaiclientAction_AIModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_Animation), gameaiclientAction_AnimationWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_AutoMode), gameaiclientAction_AutoModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_BuffControl), gameaiclientAction_BuffControlWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_BuffShow), gameaiclientAction_BuffShowWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_BulletAdd), gameaiclientAction_BulletAddWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_BulletTransform), gameaiclientAction_BulletTransformWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_BulletBind), gameaiclientAction_BulletBindWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_IOLock), gameaiclientAction_IOLockWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_RemoveEntity), gameaiclientAction_RemoveEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_ShaderSlot), gameaiclientAction_ShaderSlotWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_SkillLocate), gameaiclientAction_SkillLocateWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_SpeakBubble), gameaiclientAction_SpeakBubbleWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_SpeedMulti), gameaiclientAction_SpeedMultiWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ai.client.Action_Transform), gameaiclientAction_TransformWrap.__Register);
        
        }
        
        static void wrapInit3(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(game.tutor.ArrowQueue), gametutorArrowQueueWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.tutor.TutorCenter), gametutorTutorCenterWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.BaseMediator), gamemediatorBaseMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.ChildMediator), gamemediatorChildMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.CoverMediator), gamemediatorCoverMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.FreeMediator), gamemediatorFreeMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.MediatorCore), gamemediatorMediatorCoreWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.PanelManager), gamemediatorPanelManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.SimpleView), gamemediatorSimpleViewWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.TempMediator), gamemediatorTempMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.setting.PanelCellType), gamemediatorsettingPanelCellTypeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.manager.EntityManager), gamemanagerEntityManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.manager.SoundManager), gamemanagerSoundManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.BridgeCallAsset), gameluaBridgeCallAssetWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.BridgeCallBuff), gameluaBridgeCallBuffWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.BridgeCallEntity), gameluaBridgeCallEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.BridgeCallSearch), gameluaBridgeCallSearchWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.StationManager), gameluaStationManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.WorldBossCreator_Template), gameluaWorldBossCreator_TemplateWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.lua.BridgeLuaMissionManager), gameluaBridgeLuaMissionManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.entry.VersionRotateBridge), gameentryVersionRotateBridgeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.EasePathType), pureutilstweenEasePathTypeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenTargetMode), pureutilstweenTweenTargetModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.EaseType), pureutilstweenEaseTypeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenCore), pureutilstweenTweenCoreWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenData), pureutilstweenTweenDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenParamater), pureutilstweenTweenParamaterWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenDirection), pureutilstweenTweenDirectionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenGroup), pureutilstweenTweenGroupWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenGroupMode), pureutilstweenTweenGroupModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenState), pureutilstweenTweenStateWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.TweenWait), pureutilstweenTweenWaitWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.extension.TweenComponentExtensions), pureutilstweenextensionTweenComponentExtensionsWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.tween.extension.TweenRectTransformExtentions), pureutilstweenextensionTweenRectTransformExtentionsWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.memory.ReportAsset), pureutilsmemoryReportAssetWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.mathTools.HashCode), pureutilsmathToolsHashCodeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.input.ButtonStatus), pureutilsinputButtonStatusWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.fileTools.Encruption), pureutilsfileToolsEncruptionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.utils.caculation.Formular), pureutilscaculationFormularWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.RectTransformExtensions), pureuiRectTransformExtensionsWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.DataProvider), pureuiDataProviderWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PBar_Dll), pureuiPBar_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PButton_Dll), pureuiPButton_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PDropDown_Dll), pureuiPDropDown_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PGradientText_Dll), pureuiPGradientText_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PIcon_Dll), pureuiPIcon_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.MovieEvent), pureuiMovieEventWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PMovieClip_Dll), pureuiPMovieClip_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PMoviePlayer_Dll), pureuiPMoviePlayer_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PPanel_Dll), pureuiPPanel_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PPressable_Dll), pureuiPPressable_DllWrap.__Register);
        
        }
        
        static void wrapInit4(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(pure.ui.PRepeatImage_Dll), pureuiPRepeatImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PStar_Dll), pureuiPStar_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PTab_Dll), pureuiPTab_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PText_Dll), pureuiPText_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PToggleGroup_Dll), pureuiPToggleGroup_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PToggle_Dll), pureuiPToggle_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PImage_Dll), pureuiPImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PPolyImage_Dll), pureuiPPolyImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.LangImage_Dll), pureuiLangImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UITweenParameter), pureuiUITweenParameterWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UITweenData), pureuiUITweenDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.NumberField_Dll), pureuiNumberField_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.stateMachine.machine.RpxLuaContext), purestateMachinemachineRpxLuaContextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.stateMachine.machine.TransitionContext), purestateMachinemachineTransitionContextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.net.socket.LuaHttp), purenetsocketLuaHttpWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.net.socket.LuaServer), purenetsocketLuaServerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ticker.SystemTime), puretickerSystemTimeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.runtime.LocalData), pureruntimeLocalDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.record.RecordManager), purerecordRecordManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.native.NativeManager), purenativeNativeManagerWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.database.config.ProtoVerion_Dll), puredatabaseconfigProtoVerion_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.cpp.CppSensitive), purecppCppSensitiveWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.assetdb.VersionData), pureassetdbVersionDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.assetdb.VersionGroup), pureassetdbVersionGroupWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.assetdb.IconUtils), pureassetdbIconUtilsWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PRepeatImage_Dll.Direction), pureuiPRepeatImage_DllDirectionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.NumberField_Dll.NumberChangeMode), pureuiNumberField_DllNumberChangeModeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.database.config.ProtoVerion_Dll.History), puredatabaseconfigProtoVerion_DllHistoryWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(System.ValueType), SystemValueTypeWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.Motion), UnityEngineMotionWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIColorGroup_Dll), pureuiUIColorGroup_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UITween_Dll), pureuiUITween_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.RTEffect_Dll), gameuiRTEffect_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.EventSystems.UIBehaviour), UnityEngineEventSystemsUIBehaviourWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.RTSpace_Dll), gameuiRTSpace_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.skill.SkillControl_Dll), gameuiskillSkillControl_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.AudioIndexer_Dll), gameuiAudioIndexer_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PClickable_Dll), pureuiPClickable_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.UI.Selectable), UnityEngineUISelectableWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.Draggable_Dll), gamemonoDraggable_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupActive_Dll), gameuiGroupActive_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupValue_Dll<int, bool>), gameuiGroupValue_Dll_2_SystemInt32SystemBoolean_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ArrayValue_Dll<bool>), gameuiArrayValue_Dll_1_SystemBoolean_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ArrayValue_Dll<UnityEngine.Color>), gameuiArrayValue_Dll_1_UnityEngineColor_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupValue_Dll<int, UnityEngine.Color>), gameuiGroupValue_Dll_2_SystemInt32UnityEngineColor_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ArrayValue_Dll<float>), gameuiArrayValue_Dll_1_SystemSingle_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupValue_Dll<int, float>), gameuiGroupValue_Dll_2_SystemInt32SystemSingle_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupIndexVisible_Dll), gameuiGroupIndexVisible_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ArrayValue_Dll<int>), gameuiArrayValue_Dll_1_SystemInt32_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.GroupValue_Dll<int, int>), gameuiGroupValue_Dll_2_SystemInt32SystemInt32_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ArrayValue_Dll<string>), gameuiArrayValue_Dll_1_SystemString_Wrap.__Register);
        
        }
        
        static void wrapInit5(LuaEnv luaenv, ObjectTranslator translator)
        {
        
            translator.DelayWrapLoader(typeof(game.ui.GroupValue_Dll<int, string>), gameuiGroupValue_Dll_2_SystemInt32SystemString_Wrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PImageBlendMode_Dll), pureuiPImageBlendMode_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.MultiBlurMask_Dll), gameuiMultiBlurMask_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.OversizeImage_Dll), pureuiOversizeImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.UI.MaskableGraphic), UnityEngineUIMaskableGraphicWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.UI.Graphic), UnityEngineUIGraphicWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.PortraitSetting_Dll), gameuiPortraitSetting_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.PRichText_Dll), gameuiPRichText_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.UI.Text), UnityEngineUITextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.PTouchableRect_Dll), pureuiPTouchableRect_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.WorldMap_Dll), gameuiWorldMap_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ComboList_Dll), gameuiComboList_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIBackground_Dll), pureuiUIBackground_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UICore), pureuiUICoreWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIBar_Dll), pureuiUIBar_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIButton_Dll), pureuiUIButton_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIButtonCore), pureuiUIButtonCoreWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UICheckBox_Dll), pureuiUICheckBox_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UICombobox_Dll), pureuiUICombobox_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UIEffect_Dll), gameuiUIEffect_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UILabel_Dll), pureuiUILabel_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UIPortrait_Dll), gameuiUIPortrait_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIPrefabReplace_Dll), pureuiUIPrefabReplace_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIRadioButton_Dll), pureuiUIRadioButton_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIResourceField_Dll), pureuiUIResourceField_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UIRichText_Dll), gameuiUIRichText_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UISlider_Dll), pureuiUISlider_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UIStar_Dll), pureuiUIStar_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UITabNavigagor_Dll), pureuiUITabNavigagor_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.UITextArea_Dll), gameuiUITextArea_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.BaseScrollPane), gameuiBaseScrollPaneWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.ui.UITextInput_Dll), pureuiUITextInput_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.data.npc.NpcRegion_Dll), gamemonodatanpcNpcRegion_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.mono.AwakableMono), gamemonomonoAwakableMonoWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.mono.Region_Dll), gamemonomonoRegion_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.mono.Teleport_Dll), gamemonomonoTeleport_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.data.npc.NpcFuncData), gamemonodatanpcNpcFuncDataWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.data.npc.NpcFuncShop_Dll), gamemonodatanpcNpcFuncShop_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mono.data.RebirthArea_Dll), gamemonodataRebirthArea_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(dl.sequence.ui.SeqImage_Dll), dlsequenceuiSeqImage_DllWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.ItemPaneCore), gameuiItemPaneCoreWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.BaseDataPane), gameuiBaseDataPaneWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.ui.MapIconBase), gameuiMapIconBaseWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(System.Enum), SystemEnumWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.stateMachine.core.ComplexEntity), purestateMachinecoreComplexEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.stateMachine.core.Entity), purestateMachinecoreEntityWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(game.mediator.GameMediator), gamemediatorGameMediatorWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.UI.Image), UnityEngineUIImageWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.stateMachine.machine.RpxContext), purestateMachinemachineRpxContextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(pure.behavior.BTContext), purebehaviorBTContextWrap.__Register);
        
        
            translator.DelayWrapLoader(typeof(UnityEngine.ScriptableObject), UnityEngineScriptableObjectWrap.__Register);
        
        }
        
        

        

        static void Init(LuaEnv luaenv, ObjectTranslator translator){
            
            wrapInit0(luaenv, translator);
            
            wrapInit1(luaenv, translator);
            
            wrapInit2(luaenv, translator);
            
            wrapInit3(luaenv, translator);
            
            wrapInit4(luaenv, translator);
            
            wrapInit5(luaenv, translator);
            
            
            translator.AddInterfaceBridgeCreator(typeof(System.Collections.IEnumerator), SystemCollectionsIEnumeratorBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(System.Collections.Generic.IList<object>), SystemCollectionsGenericIList_1_SystemObject_Bridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(System.Collections.Generic.IList<double>), SystemCollectionsGenericIList_1_SystemDouble_Bridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(System.Collections.Generic.IList<UnityEngine.Object>), SystemCollectionsGenericIList_1_UnityEngineObject_Bridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(dl.ai.scene.IPlotAnnouncePanel), dlaisceneIPlotAnnouncePanelBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.ui.ISpaceSkillListener), gameuiISpaceSkillListenerBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.mono.manager.ISceneData), gamemonomanagerISceneDataBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.ai.entity.IDropData), gameaientityIDropDataBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.ai.scenetree.IPlotPanel), gameaiscenetreeIPlotPanelBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.ai.scenetree.IPlotSpeakPanel), gameaiscenetreeIPlotSpeakPanelBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.ai.machine.vo.IAttackSource), gameaimachinevoIAttackSourceBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.tutor.ITutorNode), gametutorITutorNodeBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.mediator.bar.ILuaBar), gamemediatorbarILuaBarBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(game.lua.IEpisode), gameluaIEpisodeBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(pure.stateMachine.interfaces.ILuaAction), purestateMachineinterfacesILuaActionBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(pure.lua.ILuaActionCallBack), pureluaILuaActionCallBackBridge.__Create);
            
            translator.AddInterfaceBridgeCreator(typeof(pure.net.socket.LuaServer.IParser), purenetsocketLuaServerIParserBridge.__Create);
            
        }

	    static XLua_Gen_Initer_Register__(){
            InternalGlobalsWrap.a();
		    XLua.LuaEnv.AddIniter(Init);
		}
	}

}
namespace XLua{
	public  class ObjectTranslatorWrap{
        public static void a(){}
		static XLua.CSObjectWrap.XLua_Gen_Initer_Register__ s_gen_reg_dumb_obj = new XLua.CSObjectWrap.XLua_Gen_Initer_Register__();
		static XLua.CSObjectWrap.XLua_Gen_Initer_Register__ gen_reg_dumb_obj {get{return s_gen_reg_dumb_obj;}}
	}

	internal partial class InternalGlobalsWrap {
        public static void a(){}
	    
		delegate pure.utils.tween.TweenShake __GEN_DELEGATE0( UnityEngine.RectTransform transform);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector2> __GEN_DELEGATE1( UnityEngine.RectTransform transform,  UnityEngine.Vector2 startPosition);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector2> __GEN_DELEGATE2( UnityEngine.RectTransform transform,  UnityEngine.Vector2 endPosition);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector2> __GEN_DELEGATE3( UnityEngine.RectTransform transform);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector3> __GEN_DELEGATE4( UnityEngine.RectTransform transform,  UnityEngine.Vector3[] path);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector3> __GEN_DELEGATE5( UnityEngine.RectTransform transform,  UnityEngine.Vector3[] path,  pure.utils.tween.EasePathType pathType);
		
		delegate pure.utils.tween.TweenLite<UnityEngine.Vector3> __GEN_DELEGATE6( UnityEngine.RectTransform transform,  UnityEngine.Vector3[] path,  pure.utils.tween.EasePathType pathType,  bool updateRotation);
		
		delegate UnityEngine.Rect __GEN_DELEGATE7( UnityEngine.RectTransform transform);
		
		delegate UnityEngine.Vector2 __GEN_DELEGATE8( UnityEngine.RectTransform transform,  UnityEngine.RectTransform.Edge edgeX,  UnityEngine.RectTransform.Edge edgeY);
		
		delegate UnityEngine.Rect __GEN_DELEGATE9( UnityEngine.RectTransform transform);
		
		delegate UnityEngine.Rect __GEN_DELEGATE10( UnityEngine.RectTransform transform);
		
		delegate UnityEngine.Rect __GEN_DELEGATE11( UnityEngine.RectTransform transform,  UnityEngine.RectTransform targetCoordinator);
		
		delegate void __GEN_DELEGATE12( UnityEngine.RectTransform transform,  UnityEngine.RectTransform target);
		
		delegate void __GEN_DELEGATE13( UnityEngine.RectTransform transform,  UnityEngine.RectTransform target,  int axis);
		
		delegate void __GEN_DELEGATE14( UnityEngine.RectTransform transform,  UnityEngine.Vector2 screen,  UnityEngine.Vector2 offset);
		
		delegate void __GEN_DELEGATE15( UnityEngine.RectTransform transform,  UnityEngine.Vector2 screen,  UnityEngine.Vector2 offset,  int axis);
		
		delegate void __GEN_DELEGATE16( UnityEngine.RectTransform parent,  UnityEngine.RectTransform child,  UnityEngine.Vector2 localPosition);
		
		delegate long __GEN_DELEGATE17( System.DateTime d);
		
	    static InternalGlobalsWrap(){
		    InternalGlobals.extensionMethodMap = new Dictionary<Type, IEnumerable<MethodInfo>>()
			{
			    
				{typeof(UnityEngine.RectTransform), new List<MethodInfo>(){
				
				  new __GEN_DELEGATE0(pure.utils.tween.extension.TweenRectTransformExtentions.ShakePosition)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE1(pure.utils.tween.extension.TweenRectTransformExtentions.TweenMoveFrom)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE2(pure.utils.tween.extension.TweenRectTransformExtentions.TweenMoveTo)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE3(pure.utils.tween.extension.TweenRectTransformExtentions.TweenPosition)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE4(pure.utils.tween.extension.TweenRectTransformExtentions.TweenPath)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE5(pure.utils.tween.extension.TweenRectTransformExtentions.TweenPath)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE6(pure.utils.tween.extension.TweenRectTransformExtentions.TweenPath)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE7(pure.ui.RectTransformExtensions.GetLocalRect)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE8(pure.ui.RectTransformExtensions.GetDistanceTo)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE9(pure.ui.RectTransformExtensions.CalcScreenArea)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE10(pure.ui.RectTransformExtensions.GetScreenBounds)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE11(pure.ui.RectTransformExtensions.GetBounds)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE12(pure.ui.RectTransformExtensions.Snap)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE13(pure.ui.RectTransformExtensions.Snap)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE14(pure.ui.RectTransformExtensions.Snap)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE15(pure.ui.RectTransformExtensions.Snap)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				  new __GEN_DELEGATE16(pure.ui.RectTransformExtensions.Place)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				}},
				
				{typeof(System.DateTime), new List<MethodInfo>(){
				
				  new __GEN_DELEGATE17(pure.ticker.SystemTime.Stamp)
#if UNITY_WSA && !UNITY_EDITOR
                                      .GetMethodInfo(),
#else
                                      .Method,
#endif
				
				}},
				
			};

			InternalGlobals.genTryArrayGetPtr = StaticLuaCallbacksWrap.__tryArrayGet;
            InternalGlobals.genTryArraySetPtr = StaticLuaCallbacksWrap.__tryArraySet;
		}
	}
}
