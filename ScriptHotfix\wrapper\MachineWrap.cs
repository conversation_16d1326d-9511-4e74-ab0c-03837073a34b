﻿//this source code was auto-generated, do not modify it
using System.Collections.Generic;
using pure.stateMachine.machine;
using pure.stateMachine.builtin;
using dl.sequence.entity;
using dl.ai.machine;
using game.entity.scene;
using game.ai.machine.condition.npc;
using game.ai.machine.condition.host;
using game.ai.machine.condition.core;
using game.ai.machine.action.serv;
using game.ai.machine.action.npc;
using game.ai.machine.action.htask;
using game.ai.machine.action.host;
using game.ai.machine.action.core;
using game.ai.machine.action.client;
using game.ai.machine.action.chase;
using game.ai.machine.action.buff;
using game.ai.machine.action.assist;
using game.entry;
using pure.refactor.serialize;
using game.ai.entity;
using game.mono.mono;
using game.mono.manager;
using game.mono.data;
using game.ai.personality;
using pure.database.structure.tree;
using pure.utils.enumeration;
using UnityEngine;
using pure.utils.tween;
using pure.behavior;
using pure.assetdb;
using pure.utils.input;
namespace registerWrap {
	public class MachineWrap{

        private static bool register;
        public static void Init(){
            if(!register)new MachineWrap().__reg__();
            register = true;
        }

        private void __reg__(){ 
            __reg_action__();
            __reg_condition__();
            __reg_builtin__();
        }
		private void __reg_action__(){
			CpxMachineFactory.Register(1443055504,()=>new AIAct_AddVisibilityAux(),__aiact_addvisibilityaux_reader__);
			CpxMachineFactory.Register(-1325537400,()=>new AIAct_CircleShadown(),__aiact_circleshadown_reader__);
			CpxMachineFactory.Register(1766171234,()=>new AIAct_HudRenderer(),__aiact_hudrenderer_reader__);
			CpxMachineFactory.Register(157587893,()=>new AIAct_InitHudLabel(),__aiact_inithudlabel_reader__);
			CpxMachineFactory.Register(150035782,()=>new AIAct_SequenceChildRenderer(),__aiact_sequencechildrenderer_reader__);
			CpxMachineFactory.Register(-2082903911,()=>new HostAct_SearchFightTarget(),__hostact_searchfighttarget_reader__);
			CpxMachineFactory.Register(-711233820,()=>new AIAct_FightRobot(),__aiact_fightrobot_reader__);
			CpxMachineFactory.Register(1802804093,()=>new AIAct_InitMapIcon(),__aiact_initmapicon_reader__);
			CpxMachineFactory.Register(-1847647243,()=>new SceneAct_CallEmpty(),__sceneact_callempty_reader__);
			CpxMachineFactory.Register(-1240701347,()=>new SceneAct_CallLoaded(),__sceneact_callloaded_reader__);
			CpxMachineFactory.Register(254505149,()=>new SceneAct_CallStart(),__sceneact_callstart_reader__);
			CpxMachineFactory.Register(704295258,()=>new SceneAct_ClearEntity(),__sceneact_clearentity_reader__);
			CpxMachineFactory.Register(1321589169,()=>new SceneAct_ClearFinal(),__sceneact_clearfinal_reader__);
			CpxMachineFactory.Register(1753806744,()=>new SceneAct_DestroyPrev(),__sceneact_destroyprev_reader__);
			CpxMachineFactory.Register(-649133147,()=>new SceneAct_InitStarter(),__sceneact_initstarter_reader__);
			CpxMachineFactory.Register(-1183834121,()=>new SceneAct_LastLoadAction(),__sceneact_lastloadaction_reader__);
			CpxMachineFactory.Register(2143047800,()=>new SceneAct_LoadScene(),__sceneact_loadscene_reader__);
			CpxMachineFactory.Register(-1988190451,()=>new SceneAct_TransitionHide(),__sceneact_transitionhide_reader__);
			CpxMachineFactory.Register(1311592050,()=>new SceneAct_TransitionShow(),__sceneact_transitionshow_reader__);
			CpxMachineFactory.Register(-1064393071,()=>new SceneAct_UnloadScene(),__sceneact_unloadscene_reader__);
			CpxMachineFactory.Register(1514250310,()=>new SceneAct_UnlockSound(),__sceneact_unlocksound_reader__);
			CpxMachineFactory.Register(-617299228,()=>new SceneAct_WaitCharacter(),__sceneact_waitcharacter_reader__);
			CpxMachineFactory.Register(997502624,()=>new SceneAct_WaitPreShowTask(),__sceneact_waitpreshowtask_reader__);
			CpxMachineFactory.Register(924813944,()=>new SceneAct_ClearMemory(),__sceneact_clearmemory_reader__);
			CpxMachineFactory.Register(-2004284999,()=>new SceneAct_PurgeAssetBundle(),__sceneact_purgeassetbundle_reader__);
			CpxMachineFactory.Register(-735836399,()=>new SceneAct_UnloadUnusedAsset(),__sceneact_unloadunusedasset_reader__);
			CpxMachineFactory.Register(-1395610344,()=>new SceneAct_ClosePanel(),__sceneact_closepanel_reader__);
			CpxMachineFactory.Register(-1590359980,()=>new SceneAct_OpenPanel(),__sceneact_openpanel_reader__);
			CpxMachineFactory.Register(-1739309862,()=>new AIAct_ListernClientMove(),__aiact_listernclientmove_reader__);
			CpxMachineFactory.Register(1643165901,()=>new AIAct_BackActionTree(),__aiact_backactiontree_reader__);
			CpxMachineFactory.Register(975954305,()=>new AIAct_BackBirthPlace(),__aiact_backbirthplace_reader__);
			CpxMachineFactory.Register(704161233,()=>new AIAct_CallHelp(),null);
			CpxMachineFactory.Register(-1667622994,()=>new AIAct_CallPolice(),__aiact_callpolice_reader__);
			CpxMachineFactory.Register(-1043685384,()=>new AIAct_Flee(),__aiact_flee_reader__);
			CpxMachineFactory.Register(-1043632301,()=>new AIAct_Help(),__aiact_help_reader__);
			CpxMachineFactory.Register(320539021,()=>new AIAct_Investage(),__aiact_investage_reader__);
			CpxMachineFactory.Register(-118562506,()=>new AIAct_ReceiveMessage(),__aiact_receivemessage_reader__);
			CpxMachineFactory.Register(428135437,()=>new AIAct_RunActionTree(),__aiact_runactiontree_reader__);
			CpxMachineFactory.Register(858843747,()=>new AIAct_SelectSkill(),__aiact_selectskill_reader__);
			CpxMachineFactory.Register(1597437907,()=>new AIAct_SetMode(),__aiact_setmode_reader__);
			CpxMachineFactory.Register(2018305824,()=>new AIAct_Think(),__aiact_think_reader__);
			CpxMachineFactory.Register(1928478437,()=>new AIAct_WaitRebirth(),__aiact_waitrebirth_reader__);
			CpxMachineFactory.Register(-865881772,()=>new AIAct_WaitTargetRebirth(),__aiact_waittargetrebirth_reader__);
			CpxMachineFactory.Register(-1811976821,()=>new HostAct_ClearTask(),__hostact_cleartask_reader__);
			CpxMachineFactory.Register(59889735,()=>new HostAct_SetTaskStatus(),__hostact_settaskstatus_reader__);
			CpxMachineFactory.Register(-1546504862,()=>new HostAct_TaskGoEntity(),__hostact_taskgoentity_reader__);
			CpxMachineFactory.Register(-1577548056,()=>new HostAct_TaskGoPosition(),__hostact_taskgoposition_reader__);
			CpxMachineFactory.Register(516889389,()=>new HostAct_TaskGoScene(),__hostact_taskgoscene_reader__);
			CpxMachineFactory.Register(99125452,()=>new HostAct_Dead(),__hostact_dead_reader__);
			CpxMachineFactory.Register(356713947,()=>new HostAct_DetectFocus(),__hostact_detectfocus_reader__);
			CpxMachineFactory.Register(-540446373,()=>new HostAct_DetectSurround(),__hostact_detectsurround_reader__);
			CpxMachineFactory.Register(-607820229,()=>new HostAct_FarawayTarget(),__hostact_farawaytarget_reader__);
			CpxMachineFactory.Register(-1248653321,()=>new HostAct_HostExecuted(),__hostact_hostexecuted_reader__);
			CpxMachineFactory.Register(1145245162,()=>new HostAct_InitHost(),__hostact_inithost_reader__);
			CpxMachineFactory.Register(1015024502,()=>new HostAct_IOAttack(),__hostact_ioattack_reader__);
			CpxMachineFactory.Register(922078335,()=>new HostAct_IOMove(),__hostact_iomove_reader__);
			CpxMachineFactory.Register(-727194427,()=>new HostAct_IOOrient(),__hostact_ioorient_reader__);
			CpxMachineFactory.Register(-1531834163,()=>new HostAct_LookAtAttackVO(),__hostact_lookatattackvo_reader__);
			CpxMachineFactory.Register(775767270,()=>new HostAct_MarkIOTime(),__hostact_markiotime_reader__);
			CpxMachineFactory.Register(-2056512034,()=>new HostAct_MovePath(),__hostact_movepath_reader__);
			CpxMachineFactory.Register(-1514936749,()=>new HostAct_MovePush(),__hostact_movepush_reader__);
			CpxMachineFactory.Register(-1847419144,()=>new HostAct_MovePushPath(),__hostact_movepushpath_reader__);
			CpxMachineFactory.Register(-1589905659,()=>new HostAct_MoveToTarget(),__hostact_movetotarget_reader__);
			CpxMachineFactory.Register(752863882,()=>new HostAct_PickDrops(),__hostact_pickdrops_reader__);
			CpxMachineFactory.Register(754063047,()=>new HostAct_ResetAutoMode(),__hostact_resetautomode_reader__);
			CpxMachineFactory.Register(-241012300,()=>new HostAct_SetTargetSelected(),__hostact_settargetselected_reader__);
			CpxMachineFactory.Register(-1207778194,()=>new HostAct_Stand(),__hostact_stand_reader__);
			CpxMachineFactory.Register(-680042163,()=>new HostAct_TalkNpc(),__hostact_talknpc_reader__);
			CpxMachineFactory.Register(1402129625,()=>new HostAct_TeleportScene(),__hostact_teleportscene_reader__);
			CpxMachineFactory.Register(366960401,()=>new HostAct_TimeOverIO(),__hostact_timeoverio_reader__);
			CpxMachineFactory.Register(1334637747,()=>new AIAct_AddPrefab(),__aiact_addprefab_reader__);
			CpxMachineFactory.Register(-2075907952,()=>new AIAct_Attack(),__aiact_attack_reader__);
			CpxMachineFactory.Register(831403005,()=>new AIAct_BlockBySceneMode(),__aiact_blockbyscenemode_reader__);
			CpxMachineFactory.Register(-2000182898,()=>new AIAct_BlockOnBuff(),__aiact_blockonbuff_reader__);
			CpxMachineFactory.Register(1820289529,()=>new AIAct_BlockOnMode(),__aiact_blockonmode_reader__);
			CpxMachineFactory.Register(-302647973,()=>new AIAct_HasAttackVO(),__aiact_hasattackvo_reader__);
			CpxMachineFactory.Register(-219815448,()=>new AIAct_InitCameraFollow(),__aiact_initcamerafollow_reader__);
			CpxMachineFactory.Register(743852935,()=>new AIAct_InitChildRenderer(),__aiact_initchildrenderer_reader__);
			CpxMachineFactory.Register(60363947,()=>new AIAct_InitFogExplorer(),__aiact_initfogexplorer_reader__);
			CpxMachineFactory.Register(63509628,()=>new AIAct_InitLabel(),__aiact_initlabel_reader__);
			CpxMachineFactory.Register(-1972087655,()=>new AIAct_InitPathDash(),__aiact_initpathdash_reader__);
			CpxMachineFactory.Register(612386655,()=>new AIAct_InitPathGuide(),__aiact_initpathguide_reader__);
			CpxMachineFactory.Register(-600744389,()=>new AIAct_InitRenderer(),__aiact_initrenderer_reader__);
			CpxMachineFactory.Register(3273774,()=>new AIAct_Jump(),__aiact_jump_reader__);
			CpxMachineFactory.Register(2013865858,()=>new AIAct_OnHit(),__aiact_onhit_reader__);
			CpxMachineFactory.Register(-1994578609,()=>new AIAct_OnKill(),__aiact_onkill_reader__);
			CpxMachineFactory.Register(2014407730,()=>new AIAct_Panel(),__aiact_panel_reader__);
			CpxMachineFactory.Register(-1180305622,()=>new AIAct_PartOff(),__aiact_partoff_reader__);
			CpxMachineFactory.Register(1234943838,()=>new AIAct_PathFinder(),__aiact_pathfinder_reader__);
			CpxMachineFactory.Register(-404579167,()=>new AIAct_PathSourcePickup(),__aiact_pathsourcepickup_reader__);
			CpxMachineFactory.Register(-2020257465,()=>new AIAct_RemoveTarget(),__aiact_removetarget_reader__);
			CpxMachineFactory.Register(-273327118,()=>new AIAct_RemoveTargetOnRelation(),__aiact_removetargetonrelation_reader__);
			CpxMachineFactory.Register(8957396,()=>new AIAct_SearchPickTarget(),__aiact_searchpicktarget_reader__);
			CpxMachineFactory.Register(-1046523029,()=>new AIAct_SearchTarget(),__aiact_searchtarget_reader__);
			CpxMachineFactory.Register(-765862662,()=>new AIAct_SetAnimator(),__aiact_setanimator_reader__);
			CpxMachineFactory.Register(1326299826,()=>new AIAct_SetInteractive(),__aiact_setinteractive_reader__);
			CpxMachineFactory.Register(274940994,()=>new AIAct_SetVisible(),__aiact_setvisible_reader__);
			CpxMachineFactory.Register(480700746,()=>new AIAct_ClientHost(),__aiact_clienthost_reader__);
			CpxMachineFactory.Register(-244013845,()=>new AIAct_FollowPosition(),__aiact_followposition_reader__);
			CpxMachineFactory.Register(271083679,()=>new AIAct_NotifyMove(),__aiact_notifymove_reader__);
			CpxMachineFactory.Register(-1343976595,()=>new AIAct_AdjustSkillRange(),__aiact_adjustskillrange_reader__);
			CpxMachineFactory.Register(1159434957,()=>new AIAct_Chase(),__aiact_chase_reader__);
			CpxMachineFactory.Register(874636684,()=>new AIAct_FollowTarget(),__aiact_followtarget_reader__);
			CpxMachineFactory.Register(1480472080,()=>new AIAct_BuffBlind(),__aiact_buffblind_reader__);
			CpxMachineFactory.Register(1481268795,()=>new AIAct_BuffChaos(),__aiact_buffchaos_reader__);
			CpxMachineFactory.Register(-1229757627,()=>new AIAct_BuffFrozen(),__aiact_bufffrozen_reader__);
			CpxMachineFactory.Register(1477792827,()=>new AIAct_BuffVertigo(),__aiact_buffvertigo_reader__);
			CpxMachineFactory.Register(555411170,()=>new AIAct_AssistCheckDistance(),__aiact_assistcheckdistance_reader__);
			CpxMachineFactory.Register(-33822120,()=>new AIAct_AssistCheckHostMode(),null);
			CpxMachineFactory.Register(-1017846029,()=>new LoginAct_BgLoadAsset(),__loginact_bgloadasset_reader__);
			CpxMachineFactory.Register(-193468791,()=>new LoginAct_CopyBundle(),__loginact_copybundle_reader__);
			CpxMachineFactory.Register(502817594,()=>new LoginAct_LoadBundleFiles(),__loginact_loadbundlefiles_reader__);
			CpxMachineFactory.Register(27120271,()=>new LoginAct_ForceReInstall(),__loginact_forcereinstall_reader__);
			CpxMachineFactory.Register(-1678846449,()=>new LoginAct_EnterLogoHide(),__loginact_enterlogohide_reader__);
			CpxMachineFactory.Register(1725700788,()=>new LoginAct_EnterLogoShow(),__loginact_enterlogoshow_reader__);
			CpxMachineFactory.Register(-438887833,()=>new LoginAct_PreloadHide(),__loginact_preloadhide_reader__);
			CpxMachineFactory.Register(-1245395878,()=>new LoginAct_PreloadShow(),__loginact_preloadshow_reader__);
			CpxMachineFactory.Register(1307323609,()=>new LoginAct_BindWrap(),__loginact_bindwrap_reader__);
			CpxMachineFactory.Register(-320568252,()=>new LoginAct_InitConfigure(),__loginact_initconfigure_reader__);
			CpxMachineFactory.Register(2003981722,()=>new LoginAct_InitData(),__loginact_initdata_reader__);
			CpxMachineFactory.Register(1659851185,()=>new LoginAct_InitDiagram(),__loginact_initdiagram_reader__);
			CpxMachineFactory.Register(-810769450,()=>new LoginAct_InitLua(),__loginact_initlua_reader__);
			CpxMachineFactory.Register(830285093,()=>new LoginAct_InitMachine(),__loginact_initmachine_reader__);
			CpxMachineFactory.Register(1273893231,()=>new LoginAct_InitMainUI(),__loginact_initmainui_reader__);
			CpxMachineFactory.Register(-746597931,()=>new LoginAct_InitMediator(),__loginact_initmediator_reader__);
			CpxMachineFactory.Register(1447775182,()=>new LoginAct_InitScreen(),__loginact_initscreen_reader__);
			CpxMachineFactory.Register(1451885383,()=>new LoginAct_InitShader(),__loginact_initshader_reader__);
			CpxMachineFactory.Register(-1753899505,()=>new LoginAct_InitStyle(),__loginact_initstyle_reader__);
			CpxMachineFactory.Register(636186496,()=>new LoginAct_InitTree(),__loginact_inittree_reader__);
			CpxMachineFactory.Register(635932112,()=>new LoginAct_InitLang(),__loginact_initlang_reader__);
			CpxMachineFactory.Register(949252084,()=>new LoginAct_InitSensitive(),__loginact_initsensitive_reader__);
			CpxMachineFactory.Register(-1143942714,()=>new LoginAct_GetConfig(),__loginact_getconfig_reader__);
			CpxMachineFactory.Register(-109988208,()=>new LoginAct_LoadPatch(),__loginact_loadpatch_reader__);
			CpxMachineFactory.Register(1872515051,()=>new LoginAct_LoadVersion(),__loginact_loadversion_reader__);
			CpxMachineFactory.Register(1668443016,()=>new LoginAct_CallLua(),__loginact_calllua_reader__);
			CpxMachineFactory.Register(-1520153431,()=>new LoginAct_OpenView(),__loginact_openview_reader__);
			CpxMachineFactory.Register(620429825,()=>new LoginAct_SdkLogin(),__loginact_sdklogin_reader__);
			CpxMachineFactory.Register(1684512488,()=>new LoginAct_StartLua(),__loginact_startlua_reader__);
			CpxMachineFactory.Register(1419543335,()=>new CpxAct_BlockOnParameter(),__cpxact_blockonparameter_reader__);
			CpxMachineFactory.Register(-1851804735,()=>new CpxAct_DebugScope(),__cpxact_debugscope_reader__);
			CpxMachineFactory.Register(1558363373,()=>new CpxAct_Delay(),__cpxact_delay_reader__);
			CpxMachineFactory.Register(-2036779954,()=>new CpxAct_Lua(),__cpxact_lua_reader__);
			CpxMachineFactory.Register(-777406281,()=>new CpxAct_Nothing(),__cpxact_nothing_reader__);
			CpxMachineFactory.Register(70218443,()=>new CpxAct_Parallel(),__cpxact_parallel_reader__);
			CpxMachineFactory.Register(-1144926262,()=>new CpxAct_ParallelSelector(),__cpxact_parallelselector_reader__);
			CpxMachineFactory.Register(972681064,()=>new CpxAct_RandomSequence(),__cpxact_randomsequence_reader__);
			CpxMachineFactory.Register(-472136580,()=>new CpxAct_RewindParameter(),__cpxact_rewindparameter_reader__);
			CpxMachineFactory.Register(90388643,()=>new CpxAct_Selector(),__cpxact_selector_reader__);
			CpxMachineFactory.Register(248364165,()=>new CpxAct_Sequence(),__cpxact_sequence_reader__);
			CpxMachineFactory.Register(-1341717733,()=>new CpxAct_SetInited(),__cpxact_setinited_reader__);
			CpxMachineFactory.Register(807188332,()=>new CpxAct_SetParameter(),__cpxact_setparameter_reader__);
			CpxMachineFactory.Register(199312851,()=>new CpxAct_SubControl(),__cpxact_subcontrol_reader__);
			CpxMachineFactory.Register(101841068,()=>new CpxAct_RandomElement(),__cpxact_randomelement_reader__);
			CpxMachineFactory.Register(-1344376568,()=>new CpxAct_Repeat(),__cpxact_repeat_reader__);
			CpxMachineFactory.Register(786244089,()=>new CpxAct_UntilFailure(),__cpxact_untilfailure_reader__);
			CpxMachineFactory.Register(5648498,()=>new CpxAct_UntilSuccess(),__cpxact_untilsuccess_reader__);
		}
		private void __reg_condition__(){
			CpxMachineFactory.Register(385688924,()=>new AICon_HasActionTree(),__aicon_hasactiontree_reader__);
			CpxMachineFactory.Register(-1277554487,()=>new AICon_InChaseArea(),__aicon_inchasearea_reader__);
			CpxMachineFactory.Register(2127725562,()=>new AICon_InMode(),__aicon_inmode_reader__);
			CpxMachineFactory.Register(-917480411,()=>new AICon_HasTask(),__aicon_hastask_reader__);
			CpxMachineFactory.Register(366980440,()=>new AICon_IsIOFire(),__aicon_isiofire_reader__);
			CpxMachineFactory.Register(367194867,()=>new AICon_IsIOMove(),__aicon_isiomove_reader__);
			CpxMachineFactory.Register(1325559470,()=>new AICon_DistanceCheck(),__aicon_distancecheck_reader__);
			CpxMachineFactory.Register(1366352042,()=>new AICon_DistanceToHost(),__aicon_distancetohost_reader__);
			CpxMachineFactory.Register(-227030167,()=>new AICon_HasAttackVO(),__aicon_hasattackvo_reader__);
			CpxMachineFactory.Register(309070011,()=>new AICon_HasBuff(),__aicon_hasbuff_reader__);
			CpxMachineFactory.Register(1905559752,()=>new AICon_HasJumpPoint(),__aicon_hasjumppoint_reader__);
			CpxMachineFactory.Register(-63631324,()=>new AICon_HasMission(),__aicon_hasmission_reader__);
			CpxMachineFactory.Register(1160753145,()=>new AICon_HasTarget(),__aicon_hastarget_reader__);
			CpxMachineFactory.Register(1449611205,()=>new AICon_HasTeleport(),__aicon_hasteleport_reader__);
			CpxMachineFactory.Register(1546807719,()=>new AICon_InSceneSpecialMode(),__aicon_inscenespecialmode_reader__);
			CpxMachineFactory.Register(127743697,()=>new AICon_RuntimeParameter(),__aicon_runtimeparameter_reader__);
			CpxMachineFactory.Register(1527800951,()=>new AICon_Selected(),__aicon_selected_reader__);
			CpxMachineFactory.Register(1615903078,()=>new AICon_TargetMode(),__aicon_targetmode_reader__);
			CpxMachineFactory.Register(361195533,()=>new LoginCon_HasDirtyAsset(),__logincon_hasdirtyasset_reader__);
			CpxMachineFactory.Register(1079121156,()=>new LoginCon_IsAbi(),__logincon_isabi_reader__);
			CpxMachineFactory.Register(-1937453898,()=>new LoginCon_IsLowCritical(),__logincon_islowcritical_reader__);
			CpxMachineFactory.Register(-1525004086,()=>new LoginCon_IsNetwork(),__logincon_isnetwork_reader__);
			CpxMachineFactory.Register(1994815259,()=>new LoginCon_IsStreamHigh(),__logincon_isstreamhigh_reader__);
			CpxMachineFactory.Register(1541927162,()=>new LoginCon_IsVersionHigher(),__logincon_isversionhigher_reader__);
			CpxMachineFactory.Register(-1081431998,()=>new LoginCon_IsVersionUpdate(),__logincon_isversionupdate_reader__);
			CpxMachineFactory.Register(1371750136,()=>new CpxCondition_And(),__cpxcondition_and_reader__);
			CpxMachineFactory.Register(-1202668847,()=>new CpxCondition_Any(),__cpxcondition_any_reader__);
			CpxMachineFactory.Register(-108400545,()=>new CpxCondition_IsCpxInited(),__cpxcondition_iscpxinited_reader__);
			CpxMachineFactory.Register(526835715,()=>new CpxCondition_Lua(),__cpxcondition_lua_reader__);
			CpxMachineFactory.Register(-1202675550,()=>new CpxCondition_Or(),__cpxcondition_or_reader__);
			CpxMachineFactory.Register(1409768428,()=>new CpxCondition_Parameter(),__cpxcondition_parameter_reader__);
		}
		private void __reg_builtin__(){
			CpxMachineFactory.Register(-1422950858,__cpxstate_action_reader__);
			CpxMachineFactory.Register(96748,__cpxstate_any_reader__);
			CpxMachineFactory.Register(3127582,__cpxstate_exit_reader__);
			CpxMachineFactory.Register(1954460585,__cpxparameter_reader__);
			CpxMachineFactory.Register(825312327,__cpxmachine_reader__);
			CpxMachineFactory.Register(-1724158635,__cpxtransition_reader__);
		}
			private static void __aiact_addvisibilityaux_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_AddVisibilityAux obj=(AIAct_AddVisibilityAux)cpx;
				obj.priority=ba.ReadInt();
				obj.setting=(VisibleSetting)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_circleshadown_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_CircleShadown obj=(AIAct_CircleShadown)cpx;
				obj.priority=ba.ReadInt();
				obj.material=ByteArrayTools.ReadString(ba,sp);
				obj.radius=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_hudrenderer_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_HudRenderer obj=(AIAct_HudRenderer)cpx;
				obj.priority=ba.ReadInt();
				obj.hudName=ByteArrayTools.ReadString(ba,sp);
				obj.requireBone=ByteArrayTools.ReadString(ba,sp);
				obj.boneMode=(BoneBundleMode)ba.ReadInt();
				obj.support=ByteArrayTools.ReadArray<string>(ba,sp);
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_inithudlabel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitHudLabel obj=(AIAct_InitHudLabel)cpx;
				obj.priority=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.labelName=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_sequencechildrenderer_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SequenceChildRenderer obj=(AIAct_SequenceChildRenderer)cpx;
				obj.priority=ba.ReadInt();
				obj.name=ByteArrayTools.ReadString(ba,sp);
				obj.requireBone=ByteArrayTools.ReadString(ba,sp);
				obj.boneMode=(BoneBundleMode)ba.ReadInt();
				obj.support=ByteArrayTools.ReadArray<string>(ba,sp);
				obj.type=(ChildRendererType)ba.ReadInt();
				obj.setting=(ChildRendererSetting)ba.ReadInt();
				obj.frameType=(ChildRendererFrameType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_searchfighttarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_SearchFightTarget obj=(HostAct_SearchFightTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.relation=(Relation)ba.ReadInt();
				obj.once=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_fightrobot_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_FightRobot obj=(AIAct_FightRobot)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initmapicon_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitMapIcon obj=(AIAct_InitMapIcon)cpx;
				obj.priority=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_callempty_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_CallEmpty obj=(SceneAct_CallEmpty)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_callloaded_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_CallLoaded obj=(SceneAct_CallLoaded)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_callstart_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_CallStart obj=(SceneAct_CallStart)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_clearentity_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_ClearEntity obj=(SceneAct_ClearEntity)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_clearfinal_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_ClearFinal obj=(SceneAct_ClearFinal)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_destroyprev_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_DestroyPrev obj=(SceneAct_DestroyPrev)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_initstarter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_InitStarter obj=(SceneAct_InitStarter)cpx;
				obj.priority=ba.ReadInt();
				obj.content=(SceneStartContent)ba.ReadInt();
				obj.progressSlot=ByteArrayTools.ReadString(ba,sp);
				obj.initPoolStep=ba.ReadInt();
				obj.initNpcStep=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_lastloadaction_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_LastLoadAction obj=(SceneAct_LastLoadAction)cpx;
				obj.priority=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.asyncUploadBufferSize=ba.ReadInt();
				obj.asyncUploadTimeSlice=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_loadscene_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_LoadScene obj=(SceneAct_LoadScene)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_transitionhide_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_TransitionHide obj=(SceneAct_TransitionHide)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_transitionshow_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_TransitionShow obj=(SceneAct_TransitionShow)cpx;
				obj.priority=ba.ReadInt();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.slots=ByteArrayTools.ReadArray<string>(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_unloadscene_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_UnloadScene obj=(SceneAct_UnloadScene)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_unlocksound_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_UnlockSound obj=(SceneAct_UnlockSound)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_waitcharacter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_WaitCharacter obj=(SceneAct_WaitCharacter)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_waitpreshowtask_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_WaitPreShowTask obj=(SceneAct_WaitPreShowTask)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(SceneSpecialMode)ba.ReadInt();
				obj.check=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_clearmemory_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_ClearMemory obj=(SceneAct_ClearMemory)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_purgeassetbundle_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_PurgeAssetBundle obj=(SceneAct_PurgeAssetBundle)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_unloadunusedasset_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_UnloadUnusedAsset obj=(SceneAct_UnloadUnusedAsset)cpx;
				obj.priority=ba.ReadInt();
				obj.unloadUnsedAssetsInterval=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_closepanel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_ClosePanel obj=(SceneAct_ClosePanel)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.delay=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __sceneact_openpanel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				SceneAct_OpenPanel obj=(SceneAct_OpenPanel)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_listernclientmove_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_ListernClientMove obj=(AIAct_ListernClientMove)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_backactiontree_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BackActionTree obj=(AIAct_BackActionTree)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(ActionType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_backbirthplace_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BackBirthPlace obj=(AIAct_BackBirthPlace)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_callpolice_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_CallPolice obj=(AIAct_CallPolice)cpx;
				obj.priority=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_flee_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Flee obj=(AIAct_Flee)cpx;
				obj.priority=ba.ReadInt();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_help_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Help obj=(AIAct_Help)cpx;
				obj.priority=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_investage_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Investage obj=(AIAct_Investage)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_receivemessage_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_ReceiveMessage obj=(AIAct_ReceiveMessage)cpx;
				obj.priority=ba.ReadInt();
				obj.think=(ThinkPhase)ba.ReadInt();
				obj.action=(ActionType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_runactiontree_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_RunActionTree obj=(AIAct_RunActionTree)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(ActionType)ba.ReadInt();
				obj.ignore=(AIAct_RunActionTree.IgnoreMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_selectskill_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SelectSkill obj=(AIAct_SelectSkill)cpx;
				obj.priority=ba.ReadInt();
				obj.target=(TargetType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_setmode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SetMode obj=(AIAct_SetMode)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(ActionMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_think_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Think obj=(AIAct_Think)cpx;
				obj.priority=ba.ReadInt();
				obj.phase=(ThinkPhase)ba.ReadInt();
				obj.method=(ThinkMethod)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_waitrebirth_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_WaitRebirth obj=(AIAct_WaitRebirth)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_waittargetrebirth_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_WaitTargetRebirth obj=(AIAct_WaitTargetRebirth)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_cleartask_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_ClearTask obj=(HostAct_ClearTask)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_settaskstatus_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_SetTaskStatus obj=(HostAct_SetTaskStatus)cpx;
				obj.priority=ba.ReadInt();
				obj.value=(CompStatus)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_taskgoentity_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TaskGoEntity obj=(HostAct_TaskGoEntity)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.luaEnter=ByteArrayTools.ReadString(ba,sp);
				obj.luaExit=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_taskgoposition_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TaskGoPosition obj=(HostAct_TaskGoPosition)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.luaEnter=ByteArrayTools.ReadString(ba,sp);
				obj.luaExit=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_taskgoscene_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TaskGoScene obj=(HostAct_TaskGoScene)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.luaEnter=ByteArrayTools.ReadString(ba,sp);
				obj.luaExit=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_dead_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_Dead obj=(HostAct_Dead)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_detectfocus_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_DetectFocus obj=(HostAct_DetectFocus)cpx;
				obj.priority=ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.relation=(Relation)ba.ReadInt();
				obj.once=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_detectsurround_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_DetectSurround obj=(HostAct_DetectSurround)cpx;
				obj.priority=ba.ReadInt();
				obj.range=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_farawaytarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_FarawayTarget obj=(HostAct_FarawayTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.distance=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_hostexecuted_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_HostExecuted obj=(HostAct_HostExecuted)cpx;
				obj.priority=ba.ReadInt();
				obj.value=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_inithost_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_InitHost obj=(HostAct_InitHost)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_ioattack_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_IOAttack obj=(HostAct_IOAttack)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(HostAct_IOAttack.Mode)ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_iomove_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_IOMove obj=(HostAct_IOMove)cpx;
				obj.priority=ba.ReadInt();
				obj.clickTargetType=(EntityType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_ioorient_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_IOOrient obj=(HostAct_IOOrient)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_lookatattackvo_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_LookAtAttackVO obj=(HostAct_LookAtAttackVO)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_markiotime_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_MarkIOTime obj=(HostAct_MarkIOTime)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_movepath_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_MovePath obj=(HostAct_MovePath)cpx;
				obj.priority=ba.ReadInt();
				obj.notifyCommand=ba.ReadInt();
				obj.jumpIdleTime=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_movepush_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_MovePush obj=(HostAct_MovePush)cpx;
				obj.priority=ba.ReadInt();
				obj.notifier=ByteArrayTools.ReadString(ba,sp);
				obj.notifyInterval=ba.ReadFloat();
				obj.forcast=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_movepushpath_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_MovePushPath obj=(HostAct_MovePushPath)cpx;
				obj.priority=ba.ReadInt();
				obj.notifyCommand=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_movetotarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_MoveToTarget obj=(HostAct_MoveToTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.notifyCommand=ba.ReadInt();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_pickdrops_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_PickDrops obj=(HostAct_PickDrops)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_resetautomode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_ResetAutoMode obj=(HostAct_ResetAutoMode)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_settargetselected_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_SetTargetSelected obj=(HostAct_SetTargetSelected)cpx;
				obj.priority=ba.ReadInt();
				obj.value=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_stand_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_Stand obj=(HostAct_Stand)cpx;
				obj.priority=ba.ReadInt();
				obj.notifyCommand=ByteArrayTools.ReadString(ba,sp);
				obj.updateInterval=ba.ReadFloat();
				obj.weights=ByteArrayTools.ReadArray<int>(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_talknpc_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TalkNpc obj=(HostAct_TalkNpc)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_teleportscene_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TeleportScene obj=(HostAct_TeleportScene)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __hostact_timeoverio_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				HostAct_TimeOverIO obj=(HostAct_TimeOverIO)cpx;
				obj.priority=ba.ReadInt();
				obj.usrPlaceSetting=ba.ReadBool();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_addprefab_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_AddPrefab obj=(AIAct_AddPrefab)cpx;
				obj.priority=ba.ReadInt();
				obj.body=ByteArrayTools.ReadString(ba,sp);
				obj.bone=ByteArrayTools.ReadString(ba,sp);
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.offset=ByteArrayTools.ReadVec3(ba);
				obj.removeAsExit=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_attack_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Attack obj=(AIAct_Attack)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(AIAct_Attack.Mode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_blockbyscenemode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BlockBySceneMode obj=(AIAct_BlockBySceneMode)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(SceneSpecialMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_blockonbuff_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BlockOnBuff obj=(AIAct_BlockOnBuff)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(BuffControlMode)ba.ReadInt();
				obj.check=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_blockonmode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BlockOnMode obj=(AIAct_BlockOnMode)cpx;
				obj.priority=ba.ReadInt();
				obj.mode=(ActionMode)ba.ReadInt();
				obj.check=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_hasattackvo_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_HasAttackVO obj=(AIAct_HasAttackVO)cpx;
				obj.priority=ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initcamerafollow_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitCameraFollow obj=(AIAct_InitCameraFollow)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initchildrenderer_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitChildRenderer obj=(AIAct_InitChildRenderer)cpx;
				obj.priority=ba.ReadInt();
				obj.name=ByteArrayTools.ReadString(ba,sp);
				obj.requireBone=ByteArrayTools.ReadString(ba,sp);
				obj.boneMode=(BoneBundleMode)ba.ReadInt();
				obj.support=ByteArrayTools.ReadArray<string>(ba,sp);
				obj.type=(ChildRendererType)ba.ReadInt();
				obj.setting=(ChildRendererSetting)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initfogexplorer_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitFogExplorer obj=(AIAct_InitFogExplorer)cpx;
				obj.priority=ba.ReadInt();
				obj.radius=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initlabel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitLabel obj=(AIAct_InitLabel)cpx;
				obj.priority=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initpathdash_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitPathDash obj=(AIAct_InitPathDash)cpx;
				obj.priority=ba.ReadInt();
				obj.midePrefab=ByteArrayTools.ReadString(ba,sp);
				obj.endPrefab=ByteArrayTools.ReadString(ba,sp);
				obj.distance=ba.ReadFloat();
				obj.arriveDistance=ba.ReadFloat();
				obj.retryInterval=ba.ReadFloat();
				obj.retryThreshold=ba.ReadFloat();
				obj.retryAnge=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initpathguide_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitPathGuide obj=(AIAct_InitPathGuide)cpx;
				obj.priority=ba.ReadInt();
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.interval=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.angel=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_initrenderer_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_InitRenderer obj=(AIAct_InitRenderer)cpx;
				obj.priority=ba.ReadInt();
				obj.property=ByteArrayTools.ReadString(ba,sp);
				obj.type=(RendererType)ba.ReadInt();
				obj.supportAngle=ba.ReadInt();
				obj.angleOffset=ba.ReadInt();
				obj.mirror=ba.ReadBool();
				obj.mirrorSector=ba.ReadFloat();
				obj.faceCamera=ba.ReadBool();
				obj.faceCameraOffsetY=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_jump_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Jump obj=(AIAct_Jump)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_onhit_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_OnHit obj=(AIAct_OnHit)cpx;
				obj.priority=ba.ReadInt();
				obj.duration=ba.ReadFloat();
				obj.awayAnimDuration=ba.ReadFloat();
				obj.maxAngles=ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.awayFlyDuration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_onkill_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_OnKill obj=(AIAct_OnKill)cpx;
				obj.priority=ba.ReadInt();
				obj.playAnim=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.maxAngles=ba.ReadInt();
				obj.easeType=(EaseType)ba.ReadInt();
				obj.awayDuration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_panel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Panel obj=(AIAct_Panel)cpx;
				obj.priority=ba.ReadInt();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.show=ba.ReadBool();
				obj.restoreAsExit=ba.ReadBool();
				obj.runMode=(RunMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_partoff_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_PartOff obj=(AIAct_PartOff)cpx;
				obj.priority=ba.ReadInt();
				obj.part=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_pathfinder_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_PathFinder obj=(AIAct_PathFinder)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_pathsourcepickup_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_PathSourcePickup obj=(AIAct_PathSourcePickup)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_removetarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_RemoveTarget obj=(AIAct_RemoveTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.targetType=(TargetType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_removetargetonrelation_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_RemoveTargetOnRelation obj=(AIAct_RemoveTargetOnRelation)cpx;
				obj.priority=ba.ReadInt();
				obj.type=(TargetType)ba.ReadInt();
				obj.mode=(AIAct_RemoveTargetOnRelation.Mode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_searchpicktarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SearchPickTarget obj=(AIAct_SearchPickTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_searchtarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SearchTarget obj=(AIAct_SearchTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.targetType=(TargetType)ba.ReadInt();
				obj.type=(EntityType)ba.ReadInt();
				obj.relation=(Relation)ba.ReadInt();
				obj.npcType=(NpcType)ba.ReadInt();
				obj.usePlaceSetting=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_setanimator_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SetAnimator obj=(AIAct_SetAnimator)cpx;
				obj.priority=ba.ReadInt();
				obj.anim=ba.ReadInt();
				obj.type=(AnimatorControllerParameterType)ba.ReadInt();
				obj.value=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_setinteractive_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SetInteractive obj=(AIAct_SetInteractive)cpx;
				obj.priority=ba.ReadInt();
				obj.interactive=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_setvisible_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_SetVisible obj=(AIAct_SetVisible)cpx;
				obj.priority=ba.ReadInt();
				obj.visible=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_clienthost_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_ClientHost obj=(AIAct_ClientHost)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_followposition_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_FollowPosition obj=(AIAct_FollowPosition)cpx;
				obj.priority=ba.ReadInt();
				obj.type=(EntityType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_notifymove_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_NotifyMove obj=(AIAct_NotifyMove)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_adjustskillrange_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_AdjustSkillRange obj=(AIAct_AdjustSkillRange)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_chase_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_Chase obj=(AIAct_Chase)cpx;
				obj.priority=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_followtarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_FollowTarget obj=(AIAct_FollowTarget)cpx;
				obj.priority=ba.ReadInt();
				obj.interval=ba.ReadFloat();
				obj.threshold=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_buffblind_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BuffBlind obj=(AIAct_BuffBlind)cpx;
				obj.priority=ba.ReadInt();
				obj.wanderRadius=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_buffchaos_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BuffChaos obj=(AIAct_BuffChaos)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_bufffrozen_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BuffFrozen obj=(AIAct_BuffFrozen)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_buffvertigo_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_BuffVertigo obj=(AIAct_BuffVertigo)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aiact_assistcheckdistance_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AIAct_AssistCheckDistance obj=(AIAct_AssistCheckDistance)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_bgloadasset_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_BgLoadAsset obj=(LoginAct_BgLoadAsset)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_copybundle_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_CopyBundle obj=(LoginAct_CopyBundle)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_loadbundlefiles_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_LoadBundleFiles obj=(LoginAct_LoadBundleFiles)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_forcereinstall_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_ForceReInstall obj=(LoginAct_ForceReInstall)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_enterlogohide_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_EnterLogoHide obj=(LoginAct_EnterLogoHide)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_enterlogoshow_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_EnterLogoShow obj=(LoginAct_EnterLogoShow)cpx;
				obj.priority=ba.ReadInt();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.duration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_preloadhide_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_PreloadHide obj=(LoginAct_PreloadHide)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_preloadshow_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_PreloadShow obj=(LoginAct_PreloadShow)cpx;
				obj.priority=ba.ReadInt();
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_bindwrap_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_BindWrap obj=(LoginAct_BindWrap)cpx;
				obj.priority=ba.ReadInt();
				obj.bindMethod=(BindMethod)ba.ReadInt();
				obj.async=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initconfigure_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitConfigure obj=(LoginAct_InitConfigure)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initdata_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitData obj=(LoginAct_InitData)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initdiagram_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitDiagram obj=(LoginAct_InitDiagram)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initlua_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitLua obj=(LoginAct_InitLua)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initmachine_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitMachine obj=(LoginAct_InitMachine)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initmainui_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitMainUI obj=(LoginAct_InitMainUI)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initmediator_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitMediator obj=(LoginAct_InitMediator)cpx;
				obj.priority=ba.ReadInt();
				obj.file=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initscreen_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitScreen obj=(LoginAct_InitScreen)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initshader_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitShader obj=(LoginAct_InitShader)cpx;
				obj.priority=ba.ReadInt();
				obj.warmUp=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initstyle_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitStyle obj=(LoginAct_InitStyle)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.file=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_inittree_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitTree obj=(LoginAct_InitTree)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initlang_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitLang obj=(LoginAct_InitLang)cpx;
				obj.priority=ba.ReadInt();
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_initsensitive_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_InitSensitive obj=(LoginAct_InitSensitive)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_getconfig_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_GetConfig obj=(LoginAct_GetConfig)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_loadpatch_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_LoadPatch obj=(LoginAct_LoadPatch)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.purgeUnused=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_loadversion_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_LoadVersion obj=(LoginAct_LoadVersion)cpx;
				obj.priority=ba.ReadInt();
				obj.url=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_calllua_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_CallLua obj=(LoginAct_CallLua)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.luaFunc=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_openview_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_OpenView obj=(LoginAct_OpenView)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.prefab=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_sdklogin_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_SdkLogin obj=(LoginAct_SdkLogin)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __loginact_startlua_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginAct_StartLua obj=(LoginAct_StartLua)cpx;
				obj.priority=ba.ReadInt();
				obj.luaFile=ByteArrayTools.ReadString(ba,sp);
				obj.panel=ByteArrayTools.ReadString(ba,sp);
				obj.keep=ba.ReadBool();
				obj.duration=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_blockonparameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_BlockOnParameter obj=(CpxAct_BlockOnParameter)cpx;
				obj.priority=ba.ReadInt();
				obj.paramId=ba.ReadLong();
				obj.targetValue=ba.ReadFloat();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_debugscope_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_DebugScope obj=(CpxAct_DebugScope)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_delay_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Delay obj=(CpxAct_Delay)cpx;
				obj.priority=ba.ReadInt();
				obj.delay=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_lua_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Lua obj=(CpxAct_Lua)cpx;
				obj.priority=ba.ReadInt();
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_nothing_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Nothing obj=(CpxAct_Nothing)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_parallel_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Parallel obj=(CpxAct_Parallel)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_parallelselector_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_ParallelSelector obj=(CpxAct_ParallelSelector)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
				obj.stopOnfail=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_randomsequence_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_RandomSequence obj=(CpxAct_RandomSequence)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
				obj.randomMode=(CpxRandomMode)ba.ReadInt();
				obj.min=ba.ReadInt();
				obj.max=ba.ReadInt();
				obj.shuffle=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_rewindparameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_RewindParameter obj=(CpxAct_RewindParameter)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_selector_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Selector obj=(CpxAct_Selector)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
				obj.stopOnfail=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_sequence_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Sequence obj=(CpxAct_Sequence)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.returnMode=(CompositionReturnMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_setinited_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_SetInited obj=(CpxAct_SetInited)cpx;
				obj.priority=ba.ReadInt();
				obj.value=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_setparameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_SetParameter obj=(CpxAct_SetParameter)cpx;
				obj.priority=ba.ReadInt();
				obj.paramId=ba.ReadLong();
				obj.targetValue=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_subcontrol_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_SubControl obj=(CpxAct_SubControl)cpx;
				obj.priority=ba.ReadInt();
				obj.file=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_randomelement_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_RandomElement obj=(CpxAct_RandomElement)cpx;
				obj.priority=ba.ReadInt();
				obj.possible=ba.ReadFloat();
				obj.mode=ba.ReadInt();
				obj.postEnd=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_repeat_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_Repeat obj=(CpxAct_Repeat)cpx;
				obj.priority=ba.ReadInt();
				obj.maxLoops=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_untilfailure_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_UntilFailure obj=(CpxAct_UntilFailure)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxact_untilsuccess_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxAct_UntilSuccess obj=(CpxAct_UntilSuccess)cpx;
				obj.priority=ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasactiontree_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasActionTree obj=(AICon_HasActionTree)cpx;
				obj.mode=(ActionType)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_inchasearea_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_InChaseArea obj=(AICon_InChaseArea)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_inmode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_InMode obj=(AICon_InMode)cpx;
				obj.mode=(ActionMode)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hastask_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasTask obj=(AICon_HasTask)cpx;
				obj.type=(HostTaskType)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_isiofire_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_IsIOFire obj=(AICon_IsIOFire)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_isiomove_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_IsIOMove obj=(AICon_IsIOMove)cpx;
				obj.action=(IOAction)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_distancecheck_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_DistanceCheck obj=(AICon_DistanceCheck)cpx;
				obj.target=(TargetType)ba.ReadInt();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.distance=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_distancetohost_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_DistanceToHost obj=(AICon_DistanceToHost)cpx;
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.distance=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasattackvo_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasAttackVO obj=(AICon_HasAttackVO)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasbuff_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasBuff obj=(AICon_HasBuff)cpx;
				obj.mode=(BuffControlMode)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasjumppoint_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasJumpPoint obj=(AICon_HasJumpPoint)cpx;
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasmission_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasMission obj=(AICon_HasMission)cpx;
				obj.missionId=ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.debug=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hastarget_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasTarget obj=(AICon_HasTarget)cpx;
				obj.compare=ba.ReadBool();
				obj.type=(TargetType)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.includeExecute=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_hasteleport_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_HasTeleport obj=(AICon_HasTeleport)cpx;
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_inscenespecialmode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_InSceneSpecialMode obj=(AICon_InSceneSpecialMode)cpx;
				obj.mode=(SceneSpecialMode)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_runtimeparameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_RuntimeParameter obj=(AICon_RuntimeParameter)cpx;
				obj.paramType=ba.ReadInt();
				obj.compareMode=(ValueCompareType)ba.ReadInt();
				obj.valueType=(ValueMethod)ba.ReadInt();
				obj.denominatorType=ba.ReadInt();
				obj.targetValue=ba.ReadFloat();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_selected_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_Selected obj=(AICon_Selected)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __aicon_targetmode_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				AICon_TargetMode obj=(AICon_TargetMode)cpx;
				obj.compare=ba.ReadBool();
				obj.type=(TargetType)ba.ReadInt();
				obj.entityType=(EntityType)ba.ReadInt();
				obj.targetMode=(ActionMode)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_hasdirtyasset_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_HasDirtyAsset obj=(LoginCon_HasDirtyAsset)cpx;
				obj.element=(DirtyAsset)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_isabi_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsAbi obj=(LoginCon_IsAbi)cpx;
				obj.abi=(AppAbi)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_islowcritical_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsLowCritical obj=(LoginCon_IsLowCritical)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_isnetwork_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsNetwork obj=(LoginCon_IsNetwork)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_isstreamhigh_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsStreamHigh obj=(LoginCon_IsStreamHigh)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_isversionhigher_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsVersionHigher obj=(LoginCon_IsVersionHigher)cpx;
				obj.element=(VersionHigher)ba.ReadInt();
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __logincon_isversionupdate_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				LoginCon_IsVersionUpdate obj=(LoginCon_IsVersionUpdate)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_and_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_And obj=(CpxCondition_And)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_any_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_Any obj=(CpxCondition_Any)cpx;
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_iscpxinited_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_IsCpxInited obj=(CpxCondition_IsCpxInited)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_lua_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_Lua obj=(CpxCondition_Lua)cpx;
				obj.lua=ByteArrayTools.ReadString(ba,sp);
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_or_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_Or obj=(CpxCondition_Or)cpx;
				obj.compare=ba.ReadBool();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxcondition_parameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxCondition_Parameter obj=(CpxCondition_Parameter)cpx;
				obj.paramId=ba.ReadLong();
				obj.threshold=ba.ReadFloat();
				obj.compare=(ValueCompareType)ba.ReadInt();
				obj.useReplace=ba.ReadBool();
				obj.replaceLua=ByteArrayTools.ReadString(ba,sp);
			}
			private static void __cpxstate_action_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxState_Action obj=(CpxState_Action)cpx;
				obj.stateName=ByteArrayTools.ReadString(ba,sp);
				obj.isDefault=ba.ReadBool();
				obj.isEnter=ba.ReadBool();
				obj.priority=ba.ReadInt();
				obj.useStatePriority=ba.ReadBool();
				obj.loops=ba.ReadInt();
			}
			private static void __cpxstate_any_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxState_Any obj=(CpxState_Any)cpx;
				obj.stateName=ByteArrayTools.ReadString(ba,sp);
				obj.isDefault=ba.ReadBool();
				obj.isEnter=ba.ReadBool();
				obj.priority=ba.ReadInt();
			}
			private static void __cpxstate_exit_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxState_Exit obj=(CpxState_Exit)cpx;
				obj.stateName=ByteArrayTools.ReadString(ba,sp);
				obj.isDefault=ba.ReadBool();
				obj.isEnter=ba.ReadBool();
				obj.priority=ba.ReadInt();
			}
			private static void __cpxparameter_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxParameter obj=(CpxParameter)cpx;
				obj.name=ByteArrayTools.ReadString(ba,sp);
				obj.type=(CpxParameterType)ba.ReadInt();
				obj.defaultValue=ba.ReadFloat();
			}
			private static void __cpxmachine_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
			}
			private static void __cpxtransition_reader__(IByteReader ba, IList<string>sp, CpxCore cpx){
				CpxTransition obj=(CpxTransition)cpx;
				obj.selectMode=(CpxTransition.SelectMode)ba.ReadInt();
				obj.useDefaultParameter=ba.ReadBool();
				obj.canTransferToSelf=ba.ReadBool();
				obj.onewayLock=ba.ReadBool();
				obj.checkMode=(CpxTransition.CheckMode)ba.ReadInt();
				obj.priority=ba.ReadInt();
				obj.force=ba.ReadBool();
			}
	}
}
