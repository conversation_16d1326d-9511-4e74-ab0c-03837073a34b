[{"code": 504239274, "componentType": "scene:showHideHud"}, {"code": -619951625, "componentType": "scene:playOffsetBullet"}, {"code": -1158382475, "componentType": "scene:playSkeletonAnimation"}, {"code": -663963976, "componentType": "scene:con_field<PERSON><PERSON>ber"}, {"code": 822229567, "componentType": "scene:npcClosePanel"}, {"code": 212766450, "componentType": "scene:removeShadow"}, {"code": 1718466712, "componentType": "scene:plotAnnounce"}, {"code": 121097174, "componentType": "fx:server_collect"}, {"code": 1557132533, "componentType": "fx:server_pick"}, {"code": 601326598, "componentType": "fx:controlmove"}, {"code": -1886079174, "componentType": "fx:controlorient"}, {"code": 1314173063, "componentType": "fx:hold_key"}, {"code": 1018372925, "componentType": "fx:iomove<PERSON>er"}, {"code": -26613829, "componentType": "fx:press_key"}, {"code": 1800031945, "componentType": "fx:set_combo"}, {"code": 593244889, "componentType": "fx:add_buff"}, {"code": -724629869, "componentType": "fx:add_hit_power"}, {"code": 511864564, "componentType": "fx:hitbreak"}, {"code": 508632871, "componentType": "fx:hit_buff"}, {"code": 2004220156, "componentType": "fx:check_hit_target"}, {"code": -791087805, "componentType": "fx:hit_damage"}, {"code": -1784705145, "componentType": "fx:hit_end"}, {"code": 508604866, "componentType": "fx:hit_away"}, {"code": -1784706104, "componentType": "fx:hit_dnf"}, {"code": -471390227, "componentType": "fx:pull"}, {"code": -1399313190, "componentType": "fx:hit_punch"}, {"code": -471390014, "componentType": "fx:push"}, {"code": 52056837, "componentType": "fx:hit_iterator_targets"}, {"code": -1454253086, "componentType": "fx:add_runtime_property"}, {"code": 159085121, "componentType": "fx:set_runtime_property"}, {"code": 1748255454, "componentType": "fx:screen_target_rotate"}, {"code": -2143883994, "componentType": "fx:setflytext"}, {"code": -268513910, "componentType": "fx:set_hit_phase"}, {"code": 1049108314, "componentType": "fx:set_skill_ceoff"}, {"code": -1073279680, "componentType": "fx:trigger_prop_buff"}, {"code": -163731220, "componentType": "fx:call_buff"}, {"code": -1971320061, "componentType": "fx:fight_event"}, {"code": -729199358, "componentType": "fx:seqence"}, {"code": -1273800313, "componentType": "fx:selector"}, {"code": -1293970513, "componentType": "fx:parallel"}, {"code": 1430572206, "componentType": "fx:parallelSelector"}, {"code": -471282571, "componentType": "fx:time"}, {"code": -1995598741, "componentType": "fx:random"}, {"code": -1991844541, "componentType": "fx:repeat"}, {"code": 1037917690, "componentType": "fx:until<PERSON><PERSON>ss"}, {"code": -1403767308, "componentType": "fx:untilFailure"}, {"code": 1584548221, "componentType": "fx:never_stop"}, {"code": -471336310, "componentType": "fx:root"}, {"code": 617831217, "componentType": "fx:action_index"}, {"code": -360162275, "componentType": "fx:check_combo_time"}, {"code": 747603975, "componentType": "fx:check_hit_index"}, {"code": 2065588011, "componentType": "fx:check_hit_qty"}, {"code": -998294018, "componentType": "fx:check_monster_type"}, {"code": -723612138, "componentType": "fx:count_surround"}, {"code": 1841158571, "componentType": "fx:compare_hold_key_time"}, {"code": 1975603520, "componentType": "fx:is_critical_hit"}, {"code": -485418062, "componentType": "fx:is_double_hit"}, {"code": -630025995, "componentType": "fx:is_host"}, {"code": 976178175, "componentType": "fx:is_kill_hit"}, {"code": -2069923621, "componentType": "fx:on_hit"}, {"code": 2083486980, "componentType": "fx:remove_target"}, {"code": -2100785488, "componentType": "fx:search_summon"}, {"code": -2025357284, "componentType": "fx:screen_target"}, {"code": 831118168, "componentType": "fx:con_time_to_prev_firetime"}, {"code": 1102828951, "componentType": "fbx:animationmove"}, {"code": 1128160128, "componentType": "fxb:bonefx"}, {"code": 1128377202, "componentType": "fxb:bounce"}, {"code": -17260593, "componentType": "fxb:buff_area"}, {"code": 115860869, "componentType": "fxb:fxchain"}, {"code": 1499093140, "componentType": "fxb:collide"}, {"code": 1159288294, "componentType": "fxb:create"}, {"code": -885532109, "componentType": "fxb:createTrap"}, {"code": 1821992535, "componentType": "fbx:dispose<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 996952815, "componentType": "fbx:dissolve"}, {"code": -633356571, "componentType": "fxb:fall"}, {"code": -241632851, "componentType": "fbx:fetch<PERSON>arget"}, {"code": 1414486044, "componentType": "fxb:lookAt"}, {"code": -633048418, "componentType": "fxb:play"}, {"code": 511515399, "componentType": "fxb:startlighten"}, {"code": 1854360641, "componentType": "fxb:tween"}, {"code": 1854364886, "componentType": "fxb:twirl"}, {"code": -633170330, "componentType": "fxb:life"}, {"code": -1769433037, "componentType": "fbx:screenSeekTarget"}, {"code": -832803686, "componentType": "fxb:screen_target"}, {"code": -436072503, "componentType": "fxb:fly"}, {"code": 1240247407, "componentType": "fxb:flysin"}, {"code": 1242624155, "componentType": "fxb:follow"}, {"code": 1030213836, "componentType": "fxb:parabola"}, {"code": -632965662, "componentType": "fxb:seek"}, {"code": 2090883532, "componentType": "fx:emit_bounce"}, {"code": 1816990776, "componentType": "fx:emit_line"}, {"code": -1860624341, "componentType": "fx:emit_normal"}, {"code": 1556260198, "componentType": "fbx:captureRound"}, {"code": -1875985579, "componentType": "fcx:addbuff"}, {"code": 822301416, "componentType": "fcx:lockslot"}, {"code": -1008754678, "componentType": "fcx:speeddown"}, {"code": 597118466, "componentType": "fxb:pos_area"}, {"code": 1257837775, "componentType": "fxb:pos_dividspace"}, {"code": 1447218993, "componentType": "fbx:pos_host"}, {"code": 597437801, "componentType": "fxb:pos_line"}, {"code": -1374498216, "componentType": "fxb:pos_resetdestiny"}, {"code": 597616549, "componentType": "fxb:pos_ring"}, {"code": -1192905567, "componentType": "fxb:pos_screen"}, {"code": 810596901, "componentType": "fxb:pos_search_in_sight"}, {"code": -1191490661, "componentType": "fxb:pos_sector"}, {"code": -471843911, "componentType": "fx:anim"}, {"code": -1392765515, "componentType": "fx:break_time"}, {"code": 2050862758, "componentType": "fx:calc_anim_index"}, {"code": -1963805, "componentType": "fx:change_weapon"}, {"code": -452690646, "componentType": "fx:chase_move"}, {"code": 1267074394, "componentType": "fx:clear_cd_on_slot"}, {"code": 905810049, "componentType": "fx:clear_prev_skill_cd"}, {"code": -1084438165, "componentType": "fx:clear_target"}, {"code": 86223015, "componentType": "fx:correctOrient"}, {"code": -919747441, "componentType": "fx:dispatch_skill_event"}, {"code": -1715730810, "componentType": "fx:fetch_continue_skill"}, {"code": 1314766512, "componentType": "fx:fire_skill"}, {"code": 29702485, "componentType": "fx:get_anim_index"}, {"code": 1769334476, "componentType": "fx:global_time_scale"}, {"code": -809627152, "componentType": "fx:limit_attack_orient"}, {"code": 2140558042, "componentType": "fx:lookAt"}, {"code": 1459791115, "componentType": "fx:lookAtTarget"}, {"code": -1709883790, "componentType": "fx:mark_cd_clear_flag"}, {"code": -471485063, "componentType": "fx:move"}, {"code": 1079178373, "componentType": "fx:move<PERSON><PERSON>Target"}, {"code": 399017599, "componentType": "fx:npc_set_interactive"}, {"code": -1229204513, "componentType": "fx:radial_blur"}, {"code": 1620014948, "componentType": "fx:randomcurve"}, {"code": -73520986, "componentType": "fx:npc_set_mode"}, {"code": -1463702973, "componentType": "fx:set_skill_extra_cd"}, {"code": -2066959181, "componentType": "fx:set_target_limite"}, {"code": -1738347155, "componentType": "fx:shake_screen"}, {"code": -1725594521, "componentType": "fx:sound"}, {"code": 1826677248, "componentType": "fx:stopCameraFollow"}, {"code": -1948520311, "componentType": "fx:summon"}, {"code": -1725088058, "componentType": "fx:taunt"}, {"code": 756458798, "componentType": "fx:temp_antihit"}, {"code": -1560785664, "componentType": "fx:time_scale"}, {"code": -1796361558, "componentType": "fx:turnorient"}, {"code": 1096832719, "componentType": "fx:twe<PERSON><PERSON><PERSON>Tar<PERSON>"}, {"code": -422041356, "componentType": "fx:ui_effect"}, {"code": 1218802101, "componentType": "fx:unlock_bullet"}, {"code": -860460072, "componentType": "fx:waitHitDone"}, {"code": -1078755360, "componentType": "fx:hitrebound"}, {"code": 369978342, "componentType": "fx:on_hit_handler"}, {"code": -1960653039, "componentType": "fx:shield"}, {"code": -418885370, "componentType": "fx:skillprotect"}, {"code": 2046374672, "componentType": "scene:root"}, {"code": 805753879, "componentType": "scene:addPrefab"}, {"code": -1848620426, "componentType": "scene:optAnim"}, {"code": -20029872, "componentType": "scene:actB<PERSON>ble"}, {"code": -1946709395, "componentType": "scene:changeCpx"}, {"code": -1376472628, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 73574745, "componentType": "scene:actDeadAnim"}, {"code": 1073814833, "componentType": "scene:dispose"}, {"code": 1249621452, "componentType": "scene:actFight"}, {"code": -700743200, "componentType": "scene:lookAt"}, {"code": -1283687164, "componentType": "scene:play<PERSON><PERSON><PERSON>"}, {"code": -589400620, "componentType": "scene:playFx"}, {"code": 521977187, "componentType": "scene:playPlot"}, {"code": 729556526, "componentType": "scene:setChildActive"}, {"code": 195019159, "componentType": "scene:set<PERSON>xTarget"}, {"code": 1491308747, "componentType": "scene:setGameObjectActive"}, {"code": 937345543, "componentType": "scene:set<PERSON><PERSON><PERSON><PERSON>"}, {"code": 1169832825, "componentType": "scene:setOcclusion"}, {"code": 1816107232, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON>"}, {"code": -985965407, "componentType": "scene:sound"}, {"code": 1124529180, "componentType": "scene:soundSource"}, {"code": 1210151716, "componentType": "scene:soundVolumn"}, {"code": 517526549, "componentType": "scene:actSpeakNormal"}, {"code": 1819235606, "componentType": "scene:actSpeakPanel"}, {"code": -1281056301, "componentType": "scene:activateStation"}, {"code": -2020003333, "componentType": "scene:showStationPanel"}, {"code": 1874683583, "componentType": "scene:teleportActive"}, {"code": 477222217, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -1455979390, "componentType": "scene:optSpeed"}, {"code": 1728580721, "componentType": "scene:seq"}, {"code": 1384601484, "componentType": "scene:seq_sel"}, {"code": 1728577709, "componentType": "scene:pal"}, {"code": -1397043768, "componentType": "scene:pal_sel"}, {"code": 1734428780, "componentType": "scene:deco_repeater"}, {"code": -835514951, "componentType": "scene:deco_never_stop"}, {"code": 1404152921, "componentType": "scene:deco_wait"}, {"code": -969596610, "componentType": "scene:deco_until<PERSON><PERSON>ss"}, {"code": 787798832, "componentType": "scene:deco_untilFailure"}, {"code": 628217511, "componentType": "scene:deco_random"}, {"code": 1334628911, "componentType": "scene:actCamElaticity"}, {"code": -506446496, "componentType": "scene:actLockCam"}, {"code": -2025761309, "componentType": "scene:actCamOrthSize"}, {"code": 245479107, "componentType": "scene:actRelease"}, {"code": 1394971394, "componentType": "scene:actCameraTween"}, {"code": 702896097, "componentType": "scene:con_action_mode"}, {"code": 651494537, "componentType": "scene:con_career"}, {"code": 772233861, "componentType": "scene:con_distanceCompare"}, {"code": 769585100, "componentType": "scene:con_gender"}, {"code": 856671517, "componentType": "scene:con_hasMission"}, {"code": -842920867, "componentType": "scene:con_hasScene"}, {"code": 520400101, "componentType": "scene:con_hasstation"}, {"code": -841844545, "componentType": "scene:con_hasThing"}, {"code": -1943301179, "componentType": "scene:con_hero"}, {"code": -129305553, "componentType": "scene:con_nav<PERSON><PERSON><PERSON><PERSON>"}, {"code": -1904722108, "componentType": "scene:con_nearest<PERSON>bj"}, {"code": -2042214410, "componentType": "scene:con_enter_idle_count"}, {"code": -1965308896, "componentType": "scene:con_property"}, {"code": 1530041595, "componentType": "scene:collide<PERSON><PERSON>"}, {"code": -1068714081, "componentType": "scene:dissolve"}, {"code": -1370857426, "componentType": "scene:groundColor"}, {"code": -1887109299, "componentType": "scene:footEffect"}, {"code": 256292241, "componentType": "scene:footprint"}, {"code": 1328327180, "componentType": "scene:scratch"}, {"code": 1470843572, "componentType": "scene:setShadow"}, {"code": -1061069629, "componentType": "scene:fxColor"}, {"code": 813652171, "componentType": "scene:disperTile"}, {"code": 1664162802, "componentType": "scene:<PERSON><PERSON><PERSON>"}, {"code": -1676877694, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 2046142044, "componentType": "scene:jump"}, {"code": 1443177947, "componentType": "scene:linear<PERSON>hase"}, {"code": -1760514327, "componentType": "scene:act<PERSON><PERSON>"}, {"code": 319021549, "componentType": "scene:actPosition"}, {"code": -1452205210, "componentType": "scene:tweenColor"}, {"code": -1810941073, "componentType": "scene:tween<PERSON><PERSON><PERSON>"}, {"code": -1722467166, "componentType": "scene:tween<PERSON><PERSON>"}, {"code": 1061907394, "componentType": "scene:tween<PERSON><PERSON>"}, {"code": 1936886342, "componentType": "scene:tweenPosition"}, {"code": -1437797043, "componentType": "scene:tweenScale"}, {"code": -740351023, "componentType": "scene:tweenTranslate"}, {"code": 917720936, "componentType": "scene:tweenChildPosition"}, {"code": 260757845, "componentType": "scene:act<PERSON>ock<PERSON>"}, {"code": 1753624019, "componentType": "scene:waitButtonClick"}, {"code": -1026035391, "componentType": "scene:adhere"}, {"code": -1473126153, "componentType": "scene:callMakeInteractiveDrop"}, {"code": 931290905, "componentType": "scene:callNpcFunc"}, {"code": 1125722000, "componentType": "scene:center<PERSON><PERSON>"}, {"code": -1725793425, "componentType": "scene:changeActionSource"}, {"code": -218152671, "componentType": "scene:changeMode"}, {"code": -179039622, "componentType": "scene:changeRelation"}, {"code": 1833463092, "componentType": "scene:changeThink"}, {"code": -1328840819, "componentType": "scene:clearAction<PERSON>ache"}, {"code": -1021672209, "componentType": "scene:fight<PERSON><PERSON>"}, {"code": -783944549, "componentType": "scene:is<PERSON><PERSON>"}, {"code": -286846851, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 932349124, "componentType": "scene:openDropBox"}, {"code": -390615926, "componentType": "scene:showNpcFuncPanel"}, {"code": -37152729, "componentType": "scene:npcOpenPanel"}, {"code": 517497932, "componentType": "scene:npcPatrolArea"}, {"code": 517928932, "componentType": "scene:npcPatrolPath"}, {"code": 674796596, "componentType": "scene:npcPartrolRange"}, {"code": 1723378293, "componentType": "scene:personality<PERSON>roperty"}, {"code": 682982172, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 93051342, "componentType": "scene:setInteractive"}, {"code": -1129649312, "componentType": "scene:setNearByArea"}, {"code": 1463182231, "componentType": "scene:setNearByPrefab"}, {"code": -1135037740, "componentType": "scene:check_magic_box"}, {"code": -1651090376, "componentType": "scene:useSkill"}, {"code": -414730665, "componentType": "scene:waitPanelClose"}, {"code": -323984160, "componentType": "scene:collideSetting"}, {"code": -742143890, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -1830456165, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -1026607486, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -509447785, "componentType": "scene:set<PERSON>vo"}, {"code": -1615688666, "componentType": "scene:<PERSON><PERSON><PERSON>"}, {"code": -2084944194, "componentType": "scene:filmScreen"}, {"code": -1601901515, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON>"}, {"code": 904374916, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 731796565, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -786020717, "componentType": "scene:plot<PERSON>argetActorList"}, {"code": 2112035236, "componentType": "scene:actCallActiveMission"}, {"code": -1465461911, "componentType": "scene:actCallBuildingBuff"}, {"code": 1483484605, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 1374355994, "componentType": "scene:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -1265963199, "componentType": "scene:actEnterArea"}, {"code": 1396448700, "componentType": "scene:randomTeleport"}, {"code": 1460035699, "componentType": "scene:uiEffect"}, {"code": -1923396511, "componentType": "scene:showHideUI"}, {"code": -459799065, "componentType": "person:root"}, {"code": 677905082, "componentType": "person:seq"}, {"code": -925858192, "componentType": "pseron:pal"}, {"code": 2095345252, "componentType": "person:selector"}, {"code": 501759112, "componentType": "person:random"}, {"code": -581754573, "componentType": "person:it<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": 855412610, "componentType": "person:it<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": -220352215, "componentType": "person:<PERSON><PERSON><PERSON><PERSON>pM<PERSON><PERSON>"}, {"code": -303774838, "componentType": "person:iterTargetInSight"}, {"code": -1371707185, "componentType": "person:onHit"}, {"code": 426904551, "componentType": "person:on<PERSON><PERSON>"}, {"code": -89368486, "componentType": "person:property"}, {"code": 32785933, "componentType": "person:attack"}, {"code": 730782020, "componentType": "person:<PERSON><PERSON><PERSON><PERSON>"}, {"code": -1854850463, "componentType": "person:<PERSON><PERSON><PERSON><PERSON>"}, {"code": -460159765, "componentType": "person:flee"}, {"code": 198636558, "componentType": "person:<PERSON><PERSON><PERSON><PERSON>"}, {"code": -1188545222, "componentType": "person:investigate"}, {"code": -10492972, "componentType": "dialog:root"}, {"code": -1946184284, "componentType": "dialog:button"}, {"code": 508007495, "componentType": "dialog:content"}, {"code": -335351347, "componentType": "dialog:group"}, {"code": 1890902816, "componentType": "panel:child_normal"}, {"code": 1849232895, "componentType": "panel:child_manual"}, {"code": 2024626997, "componentType": "panel:child_select"}, {"code": -1596821201, "componentType": "panel:child_stack"}, {"code": -630347106, "componentType": "panel:auto_close"}, {"code": 1828613706, "componentType": "panel:duration"}, {"code": -1483249278, "componentType": "panel:lua"}, {"code": -1535185404, "componentType": "panel:open_to_object"}, {"code": 1263644968, "componentType": "panel:cull"}, {"code": -460501386, "componentType": "panel:tween_screen"}, {"code": -1123250243, "componentType": "panel:animation_only"}, {"code": -832406366, "componentType": "panel:module"}, {"code": 1263629592, "componentType": "panel:cell"}, {"code": 1264086168, "componentType": "panel:root"}]