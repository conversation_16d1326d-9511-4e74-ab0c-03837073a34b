﻿//this source code was auto-generated, do not modify it
using pure.refactor.property;
using System;
using pure.refactor.serialize;
using System.Collections.Generic;
using game.mono.range;
using game.ai.entity.handler.dnf;
using game.ai.scenetree;
using game.ai.machine.vo;
using pure.utils.color;
namespace registerWrap {
	public class PropertyWrap{

        private static bool register;
        public static void Init(){
            if(!register){  
                new PropertyWrap().Register();
            }
            register = true;
        }


        private void Register(){  
            RegProperty();
        }
		internal void RegProperty(){
			EditableProperty.ClearPropertyCache();
			ByteArrayTools.Register(__sightdata_wrapper);
			ByteArrayTools.Register(__punchpower_wrapper);
			ByteArrayTools.Register(__targetobject_wrapper);
			ByteArrayTools.Register(__combovo_wrapper);
			ByteArrayTools.Register(__colormatrix_wrapper);
		}
		private SightData __sightdata_wrapper(IByteReader ba, IList<string>sp){
			SightData o = new SightData();
			o.type=(game.mono.range.SightShape)ba.ReadInt();
			o.radius=ba.ReadFloat();
			o.angle=ba.ReadFloat();
			o.thickness=ba.ReadFloat();
			o.offset=ByteArrayTools.ReadVec3(ba);
			o.rotate=ba.ReadFloat();
			return o;
		}
		private PunchPower __punchpower_wrapper(IByteReader ba, IList<string>sp){
			PunchPower o = new PunchPower();
			o.power=ba.ReadFloat();
			o.maxPower=ba.ReadFloat();
			o.duration=ba.ReadFloat();
			o.gravity=ba.ReadFloat();
			o.angle=ByteArrayTools.ReadVec2(ba);
			o.delayBack=ByteArrayTools.ReadVec2(ba);
			return o;
		}
		private TargetObject __targetobject_wrapper(IByteReader ba, IList<string>sp){
			TargetObject o = new TargetObject();
			o.mode=(game.ai.scenetree.TargetMode)ba.ReadInt();
			o.id=ba.ReadInt();
			return o;
		}
		private ComboVO __combovo_wrapper(IByteReader ba, IList<string>sp){
			ComboVO o = new ComboVO();
			o.type=ba.ReadInt();
			o.id=ba.ReadInt();
			o.duration=ba.ReadFloat();
			o.overlap=ba.ReadFloat();
			return o;
		}
		private ColorMatrix __colormatrix_wrapper(IByteReader ba, IList<string>sp){
			ColorMatrix o = new ColorMatrix();
			o.hue=ba.ReadFloat();
			o.saturation=ba.ReadFloat();
			o.brightness=ba.ReadFloat();
			o.contrast=ba.ReadFloat();
			return o;
		}
	}
}
