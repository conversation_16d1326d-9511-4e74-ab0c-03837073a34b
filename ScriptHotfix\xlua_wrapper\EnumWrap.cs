﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif
using XLua;
using System.Collections.Generic;
namespace XLua.CSObjectWrap{
      using Utils = XLua.Utils;
    public class gamemonomanagerSceneSpecialModeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(game.mono.manager.SceneSpecialMode), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(game.mono.manager.SceneSpecialMode), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(game.mono.manager.SceneSpecialMode), L, null, 5, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Plot", game.mono.manager.SceneSpecialMode.Plot);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Episode", game.mono.manager.SceneSpecialMode.Episode);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "IOLock", game.mono.manager.SceneSpecialMode.IOLock);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "PlotPrefix", game.mono.manager.SceneSpecialMode.PlotPrefix);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(game.mono.manager.SceneSpecialMode), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushgamemonomanagerSceneSpecialMode(L, (game.mono.manager.SceneSpecialMode)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Plot")){
                    translator.PushgamemonomanagerSceneSpecialMode(L, game.mono.manager.SceneSpecialMode.Plot);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Episode")){
                    translator.PushgamemonomanagerSceneSpecialMode(L, game.mono.manager.SceneSpecialMode.Episode);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "IOLock")){
                    translator.PushgamemonomanagerSceneSpecialMode(L, game.mono.manager.SceneSpecialMode.IOLock);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "PlotPrefix")){
                    translator.PushgamemonomanagerSceneSpecialMode(L, game.mono.manager.SceneSpecialMode.PlotPrefix);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for game.mono.manager.SceneSpecialMode!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for game.mono.manager.SceneSpecialMode! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class gameaientityOperationCodeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(game.ai.entity.OperationCode), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(game.ai.entity.OperationCode), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(game.ai.entity.OperationCode), L, null, 6, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "None", game.ai.entity.OperationCode.None);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Move", game.ai.entity.OperationCode.Move);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Fire", game.ai.entity.OperationCode.Fire);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Locate", game.ai.entity.OperationCode.Locate);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "LocateEnd", game.ai.entity.OperationCode.LocateEnd);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(game.ai.entity.OperationCode), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushgameaientityOperationCode(L, (game.ai.entity.OperationCode)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "None")){
                    translator.PushgameaientityOperationCode(L, game.ai.entity.OperationCode.None);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Move")){
                    translator.PushgameaientityOperationCode(L, game.ai.entity.OperationCode.Move);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Fire")){
                    translator.PushgameaientityOperationCode(L, game.ai.entity.OperationCode.Fire);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Locate")){
                    translator.PushgameaientityOperationCode(L, game.ai.entity.OperationCode.Locate);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "LocateEnd")){
                    translator.PushgameaientityOperationCode(L, game.ai.entity.OperationCode.LocateEnd);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for game.ai.entity.OperationCode!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for game.ai.entity.OperationCode! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenEasePathTypeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.EasePathType), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.EasePathType), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.EasePathType), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "LINE", pure.utils.tween.EasePathType.LINE);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CATMULLROM", pure.utils.tween.EasePathType.CATMULLROM);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.EasePathType), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenEasePathType(L, (pure.utils.tween.EasePathType)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "LINE")){
                    translator.PushpureutilstweenEasePathType(L, pure.utils.tween.EasePathType.LINE);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CATMULLROM")){
                    translator.PushpureutilstweenEasePathType(L, pure.utils.tween.EasePathType.CATMULLROM);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.EasePathType!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.EasePathType! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenTweenTargetModeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.TweenTargetMode), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.TweenTargetMode), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.TweenTargetMode), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "From", pure.utils.tween.TweenTargetMode.From);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "To", pure.utils.tween.TweenTargetMode.To);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.TweenTargetMode), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenTweenTargetMode(L, (pure.utils.tween.TweenTargetMode)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "From")){
                    translator.PushpureutilstweenTweenTargetMode(L, pure.utils.tween.TweenTargetMode.From);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "To")){
                    translator.PushpureutilstweenTweenTargetMode(L, pure.utils.tween.TweenTargetMode.To);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.TweenTargetMode!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.TweenTargetMode! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenEaseTypeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.EaseType), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.EaseType), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.EaseType), L, null, 29, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Linear", pure.utils.tween.EaseType.Linear);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Bounce", pure.utils.tween.EaseType.Bounce);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "BackEaseIn", pure.utils.tween.EaseType.BackEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "BackEaseOut", pure.utils.tween.EaseType.BackEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "BackEaseInOut", pure.utils.tween.EaseType.BackEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CircEaseIn", pure.utils.tween.EaseType.CircEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CircEaseOut", pure.utils.tween.EaseType.CircEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CircEaseInOut", pure.utils.tween.EaseType.CircEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CubicEaseIn", pure.utils.tween.EaseType.CubicEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CubicEaseOut", pure.utils.tween.EaseType.CubicEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CubicEaseInOut", pure.utils.tween.EaseType.CubicEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ExpoEaseIn", pure.utils.tween.EaseType.ExpoEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ExpoEaseOut", pure.utils.tween.EaseType.ExpoEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ExpoEaseInOut", pure.utils.tween.EaseType.ExpoEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuadEaseIn", pure.utils.tween.EaseType.QuadEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuadEaseOut", pure.utils.tween.EaseType.QuadEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuadEaseInOut", pure.utils.tween.EaseType.QuadEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuartEaseIn", pure.utils.tween.EaseType.QuartEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuartEaseOut", pure.utils.tween.EaseType.QuartEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuartEaseInOut", pure.utils.tween.EaseType.QuartEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuintEaseIn", pure.utils.tween.EaseType.QuintEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuintEaseOut", pure.utils.tween.EaseType.QuintEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "QuintEaseInOut", pure.utils.tween.EaseType.QuintEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "SineEaseIn", pure.utils.tween.EaseType.SineEaseIn);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "SineEaseOut", pure.utils.tween.EaseType.SineEaseOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "SineEaseInOut", pure.utils.tween.EaseType.SineEaseInOut);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Spring", pure.utils.tween.EaseType.Spring);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Curve", pure.utils.tween.EaseType.Curve);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.EaseType), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenEaseType(L, (pure.utils.tween.EaseType)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Linear")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.Linear);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Bounce")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.Bounce);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "BackEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.BackEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "BackEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.BackEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "BackEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.BackEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CircEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CircEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CircEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CircEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CircEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CircEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CubicEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CubicEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CubicEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CubicEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CubicEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.CubicEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ExpoEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.ExpoEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ExpoEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.ExpoEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ExpoEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.ExpoEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuadEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuadEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuadEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuadEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuadEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuadEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuartEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuartEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuartEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuartEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuartEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuartEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuintEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuintEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuintEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuintEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "QuintEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.QuintEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "SineEaseIn")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.SineEaseIn);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "SineEaseOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.SineEaseOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "SineEaseInOut")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.SineEaseInOut);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Spring")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.Spring);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Curve")){
                    translator.PushpureutilstweenEaseType(L, pure.utils.tween.EaseType.Curve);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.EaseType!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.EaseType! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenTweenParamaterWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.TweenParamater), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.TweenParamater), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.TweenParamater), L, null, 11, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "X", pure.utils.tween.TweenParamater.X);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Y", pure.utils.tween.TweenParamater.Y);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Z", pure.utils.tween.TweenParamater.Z);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ScaleX", pure.utils.tween.TweenParamater.ScaleX);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ScaleY", pure.utils.tween.TweenParamater.ScaleY);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ScaleZ", pure.utils.tween.TweenParamater.ScaleZ);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "RotateX", pure.utils.tween.TweenParamater.RotateX);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "RotateY", pure.utils.tween.TweenParamater.RotateY);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "RotateZ", pure.utils.tween.TweenParamater.RotateZ);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Alpha", pure.utils.tween.TweenParamater.Alpha);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.TweenParamater), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenTweenParamater(L, (pure.utils.tween.TweenParamater)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "X")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.X);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Y")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.Y);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Z")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.Z);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ScaleX")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.ScaleX);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ScaleY")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.ScaleY);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ScaleZ")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.ScaleZ);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "RotateX")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.RotateX);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "RotateY")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.RotateY);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "RotateZ")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.RotateZ);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Alpha")){
                    translator.PushpureutilstweenTweenParamater(L, pure.utils.tween.TweenParamater.Alpha);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.TweenParamater!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.TweenParamater! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenTweenDirectionWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.TweenDirection), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.TweenDirection), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.TweenDirection), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "FORWARD", pure.utils.tween.TweenDirection.FORWARD);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "REVERSE", pure.utils.tween.TweenDirection.REVERSE);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.TweenDirection), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenTweenDirection(L, (pure.utils.tween.TweenDirection)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "FORWARD")){
                    translator.PushpureutilstweenTweenDirection(L, pure.utils.tween.TweenDirection.FORWARD);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "REVERSE")){
                    translator.PushpureutilstweenTweenDirection(L, pure.utils.tween.TweenDirection.REVERSE);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.TweenDirection!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.TweenDirection! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenTweenGroupModeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.TweenGroupMode), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.TweenGroupMode), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.TweenGroupMode), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Sequential", pure.utils.tween.TweenGroupMode.Sequential);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Concurrent", pure.utils.tween.TweenGroupMode.Concurrent);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.TweenGroupMode), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenTweenGroupMode(L, (pure.utils.tween.TweenGroupMode)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Sequential")){
                    translator.PushpureutilstweenTweenGroupMode(L, pure.utils.tween.TweenGroupMode.Sequential);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Concurrent")){
                    translator.PushpureutilstweenTweenGroupMode(L, pure.utils.tween.TweenGroupMode.Concurrent);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.TweenGroupMode!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.TweenGroupMode! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilstweenTweenStateWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.tween.TweenState), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.tween.TweenState), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.tween.TweenState), L, null, 5, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "STOPPED", pure.utils.tween.TweenState.STOPPED);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "PAUSED", pure.utils.tween.TweenState.PAUSED);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "PLAYING", pure.utils.tween.TweenState.PLAYING);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "STARTED", pure.utils.tween.TweenState.STARTED);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.tween.TweenState), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilstweenTweenState(L, (pure.utils.tween.TweenState)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "STOPPED")){
                    translator.PushpureutilstweenTweenState(L, pure.utils.tween.TweenState.STOPPED);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "PAUSED")){
                    translator.PushpureutilstweenTweenState(L, pure.utils.tween.TweenState.PAUSED);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "PLAYING")){
                    translator.PushpureutilstweenTweenState(L, pure.utils.tween.TweenState.PLAYING);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "STARTED")){
                    translator.PushpureutilstweenTweenState(L, pure.utils.tween.TweenState.STARTED);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.tween.TweenState!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.tween.TweenState! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureutilsinputButtonStatusWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.utils.input.ButtonStatus), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.utils.input.ButtonStatus), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.utils.input.ButtonStatus), L, null, 4, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Press", pure.utils.input.ButtonStatus.Press);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Release", pure.utils.input.ButtonStatus.Release);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Click", pure.utils.input.ButtonStatus.Click);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.utils.input.ButtonStatus), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureutilsinputButtonStatus(L, (pure.utils.input.ButtonStatus)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Press")){
                    translator.PushpureutilsinputButtonStatus(L, pure.utils.input.ButtonStatus.Press);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Release")){
                    translator.PushpureutilsinputButtonStatus(L, pure.utils.input.ButtonStatus.Release);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Click")){
                    translator.PushpureutilsinputButtonStatus(L, pure.utils.input.ButtonStatus.Click);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.utils.input.ButtonStatus!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.utils.input.ButtonStatus! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureuiMovieEventWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.ui.MovieEvent), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.ui.MovieEvent), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.ui.MovieEvent), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Loop", pure.ui.MovieEvent.Loop);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Complete", pure.ui.MovieEvent.Complete);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.ui.MovieEvent), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureuiMovieEvent(L, (pure.ui.MovieEvent)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Loop")){
                    translator.PushpureuiMovieEvent(L, pure.ui.MovieEvent.Loop);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Complete")){
                    translator.PushpureuiMovieEvent(L, pure.ui.MovieEvent.Complete);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.ui.MovieEvent!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.ui.MovieEvent! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureuiUITweenParameterWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.ui.UITweenParameter), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.ui.UITweenParameter), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.ui.UITweenParameter), L, null, 8, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "X", pure.ui.UITweenParameter.X);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Y", pure.ui.UITweenParameter.Y);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ScaleX", pure.ui.UITweenParameter.ScaleX);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ScaleY", pure.ui.UITweenParameter.ScaleY);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "RotateZ", pure.ui.UITweenParameter.RotateZ);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Alpha", pure.ui.UITweenParameter.Alpha);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Color", pure.ui.UITweenParameter.Color);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.ui.UITweenParameter), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureuiUITweenParameter(L, (pure.ui.UITweenParameter)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "X")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.X);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Y")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.Y);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ScaleX")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.ScaleX);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "ScaleY")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.ScaleY);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "RotateZ")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.RotateZ);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Alpha")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.Alpha);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Color")){
                    translator.PushpureuiUITweenParameter(L, pure.ui.UITweenParameter.Color);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.ui.UITweenParameter!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.ui.UITweenParameter! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureuiPRepeatImage_DllDirectionWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.ui.PRepeatImage_Dll.Direction), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.ui.PRepeatImage_Dll.Direction), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.ui.PRepeatImage_Dll.Direction), L, null, 3, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Horiazontal", pure.ui.PRepeatImage_Dll.Direction.Horiazontal);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Vertical", pure.ui.PRepeatImage_Dll.Direction.Vertical);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.ui.PRepeatImage_Dll.Direction), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureuiPRepeatImage_DllDirection(L, (pure.ui.PRepeatImage_Dll.Direction)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Horiazontal")){
                    translator.PushpureuiPRepeatImage_DllDirection(L, pure.ui.PRepeatImage_Dll.Direction.Horiazontal);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Vertical")){
                    translator.PushpureuiPRepeatImage_DllDirection(L, pure.ui.PRepeatImage_Dll.Direction.Vertical);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.ui.PRepeatImage_Dll.Direction!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.ui.PRepeatImage_Dll.Direction! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
    public class pureuiNumberField_DllNumberChangeModeWrap
    {
		public static void __Register(RealStatePtr L)
        {
		    WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
		    Utils.BeginObjectRegister(typeof(pure.ui.NumberField_Dll.NumberChangeMode), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(pure.ui.NumberField_Dll.NumberChangeMode), L, translator, null, null, null, null, null);
			Utils.BeginClassRegister(typeof(pure.ui.NumberField_Dll.NumberChangeMode), L, null, 4, 0, 0);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Sudden", pure.ui.NumberField_Dll.NumberChangeMode.Sudden);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "CharByChar", pure.ui.NumberField_Dll.NumberChangeMode.CharByChar);
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "Roll", pure.ui.NumberField_Dll.NumberChangeMode.Roll);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "__CastFrom", __CastFrom);
            Utils.EndClassRegister(typeof(pure.ui.NumberField_Dll.NumberChangeMode), L, translator);
        }
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CastFrom(RealStatePtr L){
			WrapPusher translator = ObjectTranslatorPool.Instance.Find(L) as WrapPusher;
			LuaTypes lua_type = LuaAPI.lua_type(L, 1);
            if (lua_type == LuaTypes.LUA_TNUMBER){
                translator.PushpureuiNumberField_DllNumberChangeMode(L, (pure.ui.NumberField_Dll.NumberChangeMode)LuaAPI.xlua_tointeger(L, 1));
            }
            else if(lua_type == LuaTypes.LUA_TSTRING){
			    if (LuaAPI.xlua_is_eq_str(L, 1, "Sudden")){
                    translator.PushpureuiNumberField_DllNumberChangeMode(L, pure.ui.NumberField_Dll.NumberChangeMode.Sudden);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "CharByChar")){
                    translator.PushpureuiNumberField_DllNumberChangeMode(L, pure.ui.NumberField_Dll.NumberChangeMode.CharByChar);
                }
				else if (LuaAPI.xlua_is_eq_str(L, 1, "Roll")){
                    translator.PushpureuiNumberField_DllNumberChangeMode(L, pure.ui.NumberField_Dll.NumberChangeMode.Roll);
                }
				else {
                    return LuaAPI.luaL_error(L, "invalid string for pure.ui.NumberField_Dll.NumberChangeMode!");
                }
            }
            else {
                return LuaAPI.luaL_error(L, "invalid lua type for pure.ui.NumberField_Dll.NumberChangeMode! Expect number or string, got + " + lua_type);
            }
            return 1;
		}
	}
}
